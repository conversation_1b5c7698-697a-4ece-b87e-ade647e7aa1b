from library import *

class LoadData(QtCore.QThread):
    editCellByColumnName = QtCore.pyqtSignal(int, str, str, object)
    #  def editCellByColumnName(self, row, columnName, text, table):
    cboxCheck            = QtCore.pyqtSignal(int, object, int)
    EnableStart          = QtCore.pyqtSignal(int, object, int)
    def __init__(self, parent):
        super().__init__()
        self.parent = parent
    
    def updateFile(self):
        # Đọc dữ liệu từ file
        with open(f"{pathData}\\{self.parent.folderCbox.currentText()}.txt", 'r',encoding='utf-8') as file:
            lines = file.readlines()
        lines = [line for line in lines if line.strip()]

        with open(f"{pathData}\\{self.parent.folderCbox.currentText()}.txt", 'w',encoding='utf-8') as file:
            file.write(''.join(lines))

    def run(self):
        """
        Thêm dữ liệu vào bảng tablewidget
        """
        try:
            with open(f"{pathConfig}/config.json", "r") as file:
                dataJson = json.load(file)
        except:
            dataJson = {}
        i = 0
        self.parent.tableWidget.setRowCount(0)
        with open(f"{pathData}\\{self.parent.folderCbox.currentText()}.txt", 'r',encoding='utf-8') as file:
            data = file.read().strip().split('\n')
            data = [s for s in data if len(s) > 0] # Sử dụng list comprehension để lọc ra các chuỗi có độ dài lớn hơn 0 sau khi loại bỏ khoảng trắng
            self.updateFile()
            for row_number, line in enumerate(data):
                self.parent.tableWidget.insertRow(self.parent.tableWidget.rowCount())
                columns = line.strip().split('|')
                for column_number, value in enumerate(columns):
                    try:
                        try:
                            self.editCellByColumnName.emit(row_number, str(list(dataJson['inputFormat'].values())[column_number][1]), str(value) ,self.parent.tableWidget)
                        except:
                            self.editCellByColumnName.emit(row_number, str(dataJson['inputFormat'][column_number]), str(value) ,self.parent.tableWidget)
                    except:pass
                    if column_number == 0: i+=1;value = i;self.EnableStart.emit(value-1,self.parent.tableWidget,14);self.cboxCheck.emit(value-1,self.parent.tableWidget,0);self.editCellByColumnName.emit(row_number, 'STT', str(value) ,self.parent.tableWidget)
                self.editCellByColumnName.emit(row_number, 'Status', "Chưa chạy",self.parent.tableWidget)
                time.sleep(0.0001)
class LoadDataChrome(QtCore.QThread):
    setItem = QtCore.pyqtSignal(int, int, str, object)
    def __init__(self, parent):
        super().__init__()
        self.parent = parent
    

    def run(self):
        """
        Thêm dữ liệu vào bảng tablewidgetChrome
        """
        self.parent.tableWidgetChrome.setRowCount(0)
        i = 0
        file_and_folder_list = os.listdir(pathChrome)
        

        # Loop through each file and folder in the list
        for item in file_and_folder_list:
            full_path = os.path.join(pathChrome, item)

            if os.path.isdir(full_path):
                creation_time = os.path.getctime(full_path)

                creation_time_formatted = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(creation_time))

                self.parent.tableWidgetChrome.insertRow(self.parent.tableWidgetChrome.rowCount())

                self.setItem.emit(i, 0, str(i+1) ,self.parent.tableWidgetChrome)
                self.setItem.emit(i, 1, str(item) ,self.parent.tableWidgetChrome)
                self.setItem.emit(i, 2, str(creation_time_formatted) ,self.parent.tableWidgetChrome)
                self.setItem.emit(i, 3, "Chưa chạy",self.parent.tableWidgetChrome)

                i+=1
                time.sleep(0.0001)