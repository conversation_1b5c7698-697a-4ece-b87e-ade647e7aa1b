import random,io,traceback, time, threading, pyperclip, subprocess, shutil, win32gui, unidecode, sys, psutil, os, numpy, base64, re, string, hashlib, ctypes, platform, json, random, requests, locale, logging
from math import ceil
from colorama import *
from requests.auth import HTTPProxyAuth
import uiautomator2 as u2
from uiautomator2 import Direction
from PyQt5.QtGui import QPixmap
from datetime import datetime
from PyQt5.QtWidgets import QMainWindow, QApplication, QWidget, QInputDialog, QFileDialog
from bs4 import BeautifulSoup
from urllib.parse import urlencode
from selenium import webdriver
from selenium_stealth import stealth
import undetected_chromedriver as uc_chrome
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.common.exceptions import TimeoutException, WebDriverException
from PyQt5.QtCore import Qt
from PyQt5 import (
    QtCore,
    QtGui,
    QtWidgets,
    
    
)
from PyQt5.QtCore import * 
from PyQt5.QtWidgets import * 
from PyQt5.QtGui import *
from PyQt5.QtWidgets import (
    QApplication, 
    QMainWindow, 
    QLabel, 
    QPushButton, 
    QFrame,
    QFileDialog,
    QMessageBox,
    
    )

from PyQt5.QtCore import (
    QTimer,
    QEventLoop,
    QPoint
)



PyTournes = os.path.abspath(os.environ['PROGRAMDATA']+"\\PyTournes\\AutoTikTok\\")
pathConfig = os.path.abspath(PyTournes+'\\config\\')
pathData = os.path.abspath(PyTournes+'\\config\\data\\')
pathChrome = os.path.abspath(PyTournes+'\\config\\chrome\\')
pathCache = os.path.abspath(PyTournes+'\\config\\cache\\')
pathOutput = os.path.abspath(PyTournes+'\\output\\')
pathDataTikTok = os.path.abspath(PyTournes+'\\datatiktok\\')
pathHistory = os.path.abspath(PyTournes+'\\config\\history\\')
pathLog = os.path.abspath(PyTournes+'\\log\\')
currentDate = datetime.now().strftime('%d-%m-%Y')
errorlog = os.path.join(pathOutput, f'{currentDate}.log')
try:
    with open(f"{pathConfig}/config.json", "r", encoding='utf-8') as file:
        dataJson = json.load(file)
except:
    dataJson = {}
    
# print(datetime.now())
# dataJson['group'] = {'count':len(os.listdir(pathData))}
# print(dataJson)