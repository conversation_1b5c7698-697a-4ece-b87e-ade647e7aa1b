from Core import *
from src.modules import *
from src.widgets.py_toggle import PyToggle


class Ui_APP(object):
    threadOn = {}; MiningOn = []; iterDataTikTok = []; nextThread = ''
    Blacken = 0; AllTable = 0; Jobs = 0; Coin = 0; CoinR = 0;MaxDevice = 0; DOnl = 0; DId = 0; Ip = 0; timeoutSV = 0
    stackCheck = {'stackInput':False,'stackExtension':False,'stackSettings':False}
    gripSize = 16; grips = []; _old_pos = None

    def setupUi(self, APP):

                self.uiFuncions = UiFuncions(self)
                try:
                    self.config = json.loads(open(f'{pathConfig}\\config.json', 'r', encoding="utf-8-sig").read())
                except:pass
        
                APP.setObjectName("APP")
                APP.resize(1331, 772)
                APP.setAttribute(Qt.WA_TranslucentBackground)
                APP.setWindowFlags(Qt.FramelessWindowHint)
                APP.setWindowIcon(QtGui.QIcon('icon.ico'))
                self.stylesheet = QtWidgets.QWidget(APP)
                self.stylesheet.setMaximumSize(QtCore.QSize(********, ********))
                self.stylesheet.setStyleSheet("* {\n"
        "    background-color: transparent;\n"
        "    background: none;\n"
        "    border: none;\n"
        "    padding: 0;\n"
        "    margin: 0;\n"
        "    font: 10pt \"Segoe UI\";\n"
        "    background: no-repeat;\n"
        "    color: #fff;\n"
        "}\n"
        "\n"
        "QWidget {\n"
        "    color: rgb(221, 221, 221);\n"
        "    font: 10pt \"Segoe UI\";\n"
        "}\n"
        "\n"
        "#MainPages {\n"
        "    border-radius: 5px;\n"
        "    background-color: #2c313c;\n"
        "}\n"
        "\n"
        "#farmeleftMenu QPushButton {\n"
        "    text-align: left;\n"
        "    padding: 3px 17px;\n"
        "    padding-right: 75px;\n"
        "}\n"
        "\n"
        "\n"
        "#leftMenuSettings {\n"
        "    background-color: #343b48;\n"
        "}\n"
        "\n"
        "#frameLeftTop {\n"
        "    background-color: #3c4454;\n"
        "\n"
        "}\n"
        "\n"
        "\n"
        "/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
        "Right Pages */\n"
        "#fullrightPages {\n"
        "    background-color: #272c36;\n"
        "}\n"
        "\n"
        "\n"
        "/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
        "LineEdit */\n"
        "QLineEdit {\n"
        "    background-color: #1b1e23;\n"
        "    border-radius: 5px;\n"
        "    border: 2px solid rgb(33, 37, 43);\n"
        "    padding-left: 10px;\n"
        "    selection-color: rgb(255, 255, 255);\n"
        "    selection-background-color: rgb(255, 121, 198);\n"
        "}\n"
        "\n"
        "QLineEdit:hover {\n"
        "    border: 2px solid rgb(64, 71, 88);\n"
        "}\n"
        "\n"
        "QLineEdit:focus {\n"
        "    border: 2px solid rgb(91, 101, 124);\n"
        "}\n"
        "\n"
        "/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
        "PlainTextEdit */\n"
        "QPlainTextEdit {\n"
        "    background-color: rgb(27, 29, 35);\n"
        "    border-radius: 5px;\n"
        "    padding: 10px;\n"
        "    selection-color: rgb(255, 255, 255);\n"
        "    selection-background-color: rgb(255, 121, 198);\n"
        "}\n"
        "\n"
        "QPlainTextEdit QScrollBar:vertical {\n"
        "    width: 8px;\n"
        "}\n"
        "\n"
        "QPlainTextEdit QScrollBar:horizontal {\n"
        "    height: 8px;\n"
        "}\n"
        "\n"
        "QPlainTextEdit:hover {\n"
        "    border: 2px solid rgb(64, 71, 88);\n"
        "}\n"
        "\n"
        "QPlainTextEdit:focus {\n"
        "    border: 2px solid rgb(91, 101, 124);\n"
        "}\n"
        "\n"
        "/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
        "ScrollBars */\n"
        "QScrollBar:horizontal {\n"
        "    border: none;\n"
        "    background: rgb(52, 59, 72);\n"
        "    height: 8px;\n"
        "    margin: 0px 21px 0 21px;\n"
        "    border-radius: 0px;\n"
        "}\n"
        "\n"
        "QScrollBar::handle:horizontal {\n"
        "    background: rgb(189, 147, 249);\n"
        "    min-width: 25px;\n"
        "    border-radius: 4px\n"
        "}\n"
        "\n"
        "QScrollBar::add-line:horizontal {\n"
        "    border: none;\n"
        "    background: rgb(55, 63, 77);\n"
        "    width: 20px;\n"
        "    border-top-right-radius: 4px;\n"
        "    border-bottom-right-radius: 4px;\n"
        "    subcontrol-position: right;\n"
        "    subcontrol-origin: margin;\n"
        "}\n"
        "\n"
        "QScrollBar::sub-line:horizontal {\n"
        "    border: none;\n"
        "    background: rgb(55, 63, 77);\n"
        "    width: 20px;\n"
        "    border-top-left-radius: 4px;\n"
        "    border-bottom-left-radius: 4px;\n"
        "    subcontrol-position: left;\n"
        "    subcontrol-origin: margin;\n"
        "}\n"
        "\n"
        "QScrollBar::up-arrow:horizontal,\n"
        "QScrollBar::down-arrow:horizontal {\n"
        "    background: none;\n"
        "}\n"
        "\n"
        "QScrollBar::add-page:horizontal,\n"
        "QScrollBar::sub-page:horizontal {\n"
        "    background: none;\n"
        "}\n"
        "\n"
        "QScrollBar:vertical {\n"
        "    border: none;\n"
        "    background: rgb(52, 59, 72);\n"
        "    width: 8px;\n"
        "    margin: 21px 0 21px 0;\n"
        "    border-radius: 0px;\n"
        "}\n"
        "\n"
        "QScrollBar::handle:vertical {\n"
        "    background: rgb(189, 147, 249);\n"
        "    min-height: 25px;\n"
        "    border-radius: 4px\n"
        "}\n"
        "\n"
        "QScrollBar::add-line:vertical {\n"
        "    border: none;\n"
        "    background: rgb(55, 63, 77);\n"
        "    height: 20px;\n"
        "    border-bottom-left-radius: 4px;\n"
        "    border-bottom-right-radius: 4px;\n"
        "    subcontrol-position: bottom;\n"
        "    subcontrol-origin: margin;\n"
        "}\n"
        "\n"
        "QScrollBar::sub-line:vertical {\n"
        "    border: none;\n"
        "    background: rgb(55, 63, 77);\n"
        "    height: 20px;\n"
        "    border-top-left-radius: 4px;\n"
        "    border-top-right-radius: 4px;\n"
        "    subcontrol-position: top;\n"
        "    subcontrol-origin: margin;\n"
        "}\n"
        "\n"
        "QScrollBar::up-arrow:vertical,\n"
        "QScrollBar::down-arrow:vertical {\n"
        "    background: none;\n"
        "}\n"
        "\n"
        "QScrollBar::add-page:vertical,\n"
        "QScrollBar::sub-page:vertical {\n"
        "    background: none;\n"
        "}\n"
        "\n"
        "/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
        "CheckBox */\n"
        "QCheckBox::indicator {\n"
        "    border: 3px solid rgb(52, 59, 72);\n"
        "    width: 15px;\n"
        "    height: 15px;\n"
        "    border-radius: 5px;\n"
        "    background: rgb(44, 49, 60);\n"
        "}\n"
        "\n"
        "QCheckBox::indicator:hover {\n"
        "    border: 3px solid rgb(58, 66, 81);\n"
        "}\n"
        "\n"
        "QCheckBox::indicator:checked {\n"
        "    background: 3px solid rgb(52, 59, 72);\n"
        "    border: 3px solid rgb(52, 59, 72);\n"
        "    background-image: url(:/icon/images/icons/cil-check-alt.png);\n"
        "}\n"
        "\n"
        "/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
        "RadioButton */\n"
        "QRadioButton::indicator {\n"
        "    border: 3px solid rgb(52, 59, 72);\n"
        "    width: 15px;\n"
        "    height: 15px;\n"
        "    border-radius: 10px;\n"
        "    background: rgb(44, 49, 60);\n"
        "}\n"
        "\n"
        "QRadioButton::indicator:hover {\n"
        "    border: 3px solid rgb(58, 66, 81);\n"
        "}\n"
        "\n"
        "QRadioButton::indicator:checked {\n"
        "    background: 3px solid rgb(94, 106, 130);\n"
        "    border: 3px solid rgb(52, 59, 72);\n"
        "}\n"
        "\n"
        "/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
        "ComboBox */\n"
        "QComboBox {\n"
        "    background-color: rgb(27, 29, 35);\n"
        "    border-radius: 5px;\n"
        "    border: 2px solid rgb(33, 37, 43);\n"
        "    padding: 5px;\n"
        "    padding-left: 10px;\n"
        "\n"
        "}\n"
        "\n"
        "QComboBox:hover {\n"
        "    border: 2px solid rgb(64, 71, 88);\n"
        "}\n"
        "\n"
        "QComboBox::drop-down {\n"
        "    subcontrol-origin: padding;\n"
        "    subcontrol-position: top right;\n"
        "    width: 25px;\n"
        "    border-left-width: 3px;\n"
        "    border-left-color: rgba(39, 44, 54, 150);\n"
        "    border-left-style: solid;\n"
        "    border-top-right-radius: 3px;\n"
        "    border-bottom-right-radius: 3px;\n"
        "    background-image: url(:/icon/images/icons/cil-arrow-bottom.png);\n"
        "    background-position: center;\n"
        "    background-repeat: no-reperat;\n"
        "}\n"
        "\n"
        "QComboBox QAbstractItemView {\n"
        "    color: rgb(255, 121, 198);\n"
        "    background-color: rgb(33, 37, 43);\n"
        "    padding: 10px;\n"
        "    selection-background-color: rgb(39, 44, 54);\n"
        "}\n"
        "\n"
        "/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
        "Sliders */\n"
        "QSlider::groove:horizontal {\n"
        "    border-radius: 5px;\n"
        "    height: 10px;\n"
        "    margin: 0px;\n"
        "    background-color: rgb(52, 59, 72);\n"
        "}\n"
        "\n"
        "QSlider::groove:horizontal:hover {\n"
        "    background-color: rgb(55, 62, 76);\n"
        "}\n"
        "\n"
        "QSlider::handle:horizontal {\n"
        "    background-color: rgb(189, 147, 249);\n"
        "    border: none;\n"
        "    height: 10px;\n"
        "    width: 10px;\n"
        "    margin: 0px;\n"
        "    border-radius: 5px;\n"
        "}\n"
        "\n"
        "QSlider::handle:horizontal:hover {\n"
        "    background-color: rgb(195, 155, 255);\n"
        "}\n"
        "\n"
        "QSlider::handle:horizontal:pressed {\n"
        "    background-color: rgb(255, 121, 198);\n"
        "}\n"
        "\n"
        "QSlider::groove:vertical {\n"
        "    border-radius: 5px;\n"
        "    width: 10px;\n"
        "    margin: 0px;\n"
        "    background-color: rgb(52, 59, 72);\n"
        "}\n"
        "\n"
        "QSlider::groove:vertical:hover {\n"
        "    background-color: rgb(55, 62, 76);\n"
        "}\n"
        "\n"
        "QSlider::handle:vertical {\n"
        "    background-color: rgb(189, 147, 249);\n"
        "    border: none;\n"
        "    height: 10px;\n"
        "    width: 10px;\n"
        "    margin: 0px;\n"
        "    border-radius: 5px;\n"
        "}\n"
        "\n"
        "QSlider::handle:vertical:hover {\n"
        "    background-color: rgb(195, 155, 255);\n"
        "}\n"
        "\n"
        "QSlider::handle:vertical:pressed {\n"
        "    background-color: rgb(255, 121, 198);\n"
        "}\n"
        "\n"
        "/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
        "CommandLinkButton */\n"
        "QCommandLinkButton {\n"
        "    color: rgb(255, 121, 198);\n"
        "    border-radius: 5px;\n"
        "    padding: 5px;\n"
        "    color: rgb(255, 170, 255);\n"
        "}\n"
        "\n"
        "QCommandLinkButton:hover {\n"
        "    color: rgb(255, 170, 255);\n"
        "    background-color: rgb(44, 49, 60);\n"
        "}\n"
        "\n"
        "QCommandLinkButton:pressed {\n"
        "    color: rgb(189, 147, 249);\n"
        "    background-color: rgb(52, 58, 71);\n"
        "}\n"
        "\n"
        "/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
        "QGroupBox */\n"
        "\n"
        "QGroupBox {\n"
        "    border: 1px solid rgb(221, 221, 221);\n"
        "}\n"
        "\n"
        "QGroupBox {\n"
        "    border: 1px solid #343b48;\n"
        "    margin-top: 8px;\n"
        "}\n"
        "\n"
        "QGroupBox::title {\n"
        "    subcontrol-origin: margin;\n"
        "    left: 4px;\n"
        "    padding: 0px 5px 0px 5px;\n"
        "}\n"
        "\n"
        "\n"
        "/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
        "QSpinBox */\n"
        "\n"
        "QSpinBox {\n"
        "    background-color: #1b1e23;\n"
        "}\n"
        "\n"
        "QSpinBox::down-button {\n"
        "    border-top-left-radius: 3px;\n"
        "    border-bottom-left-radius: 3px;\n"
        "    subcontrol-origin: margin;\n"
        "    subcontrol-position: center left;\n"
        "    image: url(:/icon/images/icons/cil-arrow-bottom.png);\n"
        "    background-color: #2c313c;\n"
        "    left: 1px;\n"
        "    height: 20px;\n"
        "    width: 20px;\n"
        "}\n"
        "\n"
        "QSpinBox::up-button {\n"
        "    border-top-right-radius: 3px;\n"
        "    border-bottom-right-radius: 3px;\n"
        "    subcontrol-origin: margin;\n"
        "    subcontrol-position: center right;\n"
        "    image: url(:/icon/images/icons/cil-arrow-top.png);\n"
        "    background-color: #2c313c;\n"
        "    left: 1px;\n"
        "    height: 20px;\n"
        "    width: 20px;\n"
        "}\n"
        "\n"
        "QSpinBox::up-button:pressed {\n"
        "    background-color: #6c99f4;\n"
        "}\n"
        "\n"
        "QSpinBox::down-button:pressed {\n"
        "    background-color: #6c99f4;\n"
        "}\n"
        "\n"
        "\n"
        "/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
        "QTabWidget */\n"
        "\n"
        "QTabWidget::pane {\n"
        "    border: 1px solid #272c36;\n"
        "    background: rgb(245, 245, 245);\n"
        "    right:1px;\n"
        "}\n"
        "\n"
        "QTabBar::tab {\n"
        "    text-align: left;\n"
        "    background: #343b48;\n"
        "    border: 1px solid #272c36;\n"
        "    padding: 5 3;\n"
        "    width: 115;\n"
        "}\n"
        "\n"
        "QTabBar::tab:selected {\n"
        "    background: #2c313c;\n"
        "    margin-bottom: -1px;\n"
        "}")
                self.stylesheet.setObjectName("stylesheet")
                self.verticalLayout_31 = QtWidgets.QVBoxLayout(self.stylesheet)
                self.verticalLayout_31.setContentsMargins(0, 0, 0, 0)
                self.verticalLayout_31.setSpacing(0)
                self.verticalLayout_31.setObjectName("verticalLayout_31")
                self.MainPages = QtWidgets.QFrame(self.stylesheet)
                self.MainPages.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.MainPages.setFrameShadow(QtWidgets.QFrame.Raised)
                self.MainPages.setObjectName("MainPages")
                self.horizontalLayout = QtWidgets.QHBoxLayout(self.MainPages)
                self.horizontalLayout.setContentsMargins(6, 6, 6, 6)
                self.horizontalLayout.setSpacing(0)
                self.horizontalLayout.setObjectName("horizontalLayout")
                self.fullleftMenu = QtWidgets.QFrame(self.MainPages)
                self.fullleftMenu.setMaximumSize(QtCore.QSize(60, ********))
                self.fullleftMenu.setStyleSheet("")
                self.fullleftMenu.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.fullleftMenu.setFrameShadow(QtWidgets.QFrame.Raised)
                self.fullleftMenu.setObjectName("fullleftMenu")
                self.gridLayout_19 = QtWidgets.QGridLayout(self.fullleftMenu)
                self.gridLayout_19.setContentsMargins(0, 0, 0, 0)
                self.gridLayout_19.setSpacing(0)
                self.gridLayout_19.setObjectName("gridLayout_19")
                self.farmeleftMenu = QtWidgets.QFrame(self.fullleftMenu)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.farmeleftMenu.sizePolicy().hasHeightForWidth())
                self.farmeleftMenu.setSizePolicy(sizePolicy)
                self.farmeleftMenu.setMaximumSize(QtCore.QSize(********, ********))
                self.farmeleftMenu.setSizeIncrement(QtCore.QSize(0, 0))
                self.farmeleftMenu.setLayoutDirection(QtCore.Qt.LeftToRight)
                self.farmeleftMenu.setStyleSheet("* {\n"
        "    background-color: #1b1e23;\n"
        "}\n"
        "\n"
        "QPushButton {\n"
        "    height: 40px;\n"
        "}\n"
        "\n"
        "QPushButton:hover {\n"
        "    background-color: #272c36;\n"
        "\n"
        "}\n"
        "\n"
        "QPushButton:pressed {\n"
        "    background-color: #1b1e23;\n"
        "\n"
        "}")
                self.farmeleftMenu.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.farmeleftMenu.setFrameShadow(QtWidgets.QFrame.Raised)
                self.farmeleftMenu.setObjectName("farmeleftMenu")
                self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.farmeleftMenu)
                self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
                self.verticalLayout_2.setSpacing(0)
                self.verticalLayout_2.setObjectName("verticalLayout_2")
                self.leftMenuTop = QtWidgets.QFrame(self.farmeleftMenu)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.leftMenuTop.sizePolicy().hasHeightForWidth())
                self.leftMenuTop.setSizePolicy(sizePolicy)
                self.leftMenuTop.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.leftMenuTop.setFrameShadow(QtWidgets.QFrame.Raised)
                self.leftMenuTop.setObjectName("leftMenuTop")
                self.gridLayout = QtWidgets.QGridLayout(self.leftMenuTop)
                self.gridLayout.setContentsMargins(0, 0, 0, 0)
                self.gridLayout.setSpacing(0)
                self.gridLayout.setObjectName("gridLayout")
                self.leftMenuBtn = QtWidgets.QPushButton(self.leftMenuTop)
                self.leftMenuBtn.setMaximumSize(QtCore.QSize(********, ********))
                self.leftMenuBtn.setLayoutDirection(QtCore.Qt.LeftToRight)
                self.leftMenuBtn.setStyleSheet("padding:1 17;\n"
        "text-align: left;\n"
        "border-bottom: 2px solid #3c4454;\n"
        "")
                icon = QtGui.QIcon()
                icon.addPixmap(QtGui.QPixmap(".\\images/images/menuLeft.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                self.leftMenuBtn.setIcon(icon)
                self.leftMenuBtn.setIconSize(QtCore.QSize(28, 28))
                self.leftMenuBtn.setAutoRepeatDelay(300)
                self.leftMenuBtn.setObjectName("leftMenuBtn")
                self.gridLayout.addWidget(self.leftMenuBtn, 0, 0, 1, 1)
                self.verticalLayout_2.addWidget(self.leftMenuTop)
                self.leftMenuCenter = QtWidgets.QFrame(self.farmeleftMenu)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.leftMenuCenter.sizePolicy().hasHeightForWidth())
                self.leftMenuCenter.setSizePolicy(sizePolicy)
                self.leftMenuCenter.setMaximumSize(QtCore.QSize(********, ********))
                self.leftMenuCenter.setStyleSheet("")
                self.leftMenuCenter.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.leftMenuCenter.setFrameShadow(QtWidgets.QFrame.Raised)
                self.leftMenuCenter.setLineWidth(1)
                self.leftMenuCenter.setObjectName("leftMenuCenter")
                self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.leftMenuCenter)
                self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
                self.verticalLayout_3.setSpacing(0)
                self.verticalLayout_3.setObjectName("verticalLayout_3")
                self.btn_chrome = QtWidgets.QPushButton(self.leftMenuCenter)
                icon1 = QtGui.QIcon()
                icon1.addPixmap(QtGui.QPixmap(".\\images/images/google.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                self.btn_chrome.setIcon(icon1)
                self.btn_chrome.setIconSize(QtCore.QSize(24, 24))
                self.btn_chrome.setObjectName("btn_chrome")
                self.verticalLayout_3.addWidget(self.btn_chrome)
                self.btn_tiktok = QtWidgets.QPushButton(self.leftMenuCenter)
                self.btn_tiktok.setStyleSheet("border-left:3px solid rgb(255, 255, 255);\n"
        "background-color: #2c313c;\n"
        "font:bold; \n"
        "\n"
        "")
                icon2 = QtGui.QIcon()
                icon2.addPixmap(QtGui.QPixmap(".\\images/images/tik-tok (1).png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                self.btn_tiktok.setIcon(icon2)
                self.btn_tiktok.setIconSize(QtCore.QSize(24, 24))
                self.btn_tiktok.setObjectName("btn_tiktok")
                self.verticalLayout_3.addWidget(self.btn_tiktok)
                self.btn_notify = QtWidgets.QPushButton(self.leftMenuCenter)
                icon3 = QtGui.QIcon()
                icon3.addPixmap(QtGui.QPixmap(".\\images/images/notification.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                self.btn_notify.setIcon(icon3)
                self.btn_notify.setIconSize(QtCore.QSize(24, 24))
                self.btn_notify.setObjectName("btn_notify")
                self.verticalLayout_3.addWidget(self.btn_notify)
                self.btn_help = QtWidgets.QPushButton(self.leftMenuCenter)
                icon4 = QtGui.QIcon()
                icon4.addPixmap(QtGui.QPixmap(".\\images/images/information (2).png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                self.btn_help.setIcon(icon4)
                self.btn_help.setIconSize(QtCore.QSize(24, 24))
                self.btn_help.setObjectName("btn_help")
                self.verticalLayout_3.addWidget(self.btn_help)
                self.verticalLayout_2.addWidget(self.leftMenuCenter)
                spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
                self.verticalLayout_2.addItem(spacerItem)
                self.logoutBtn = QtWidgets.QPushButton(self.farmeleftMenu)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.logoutBtn.sizePolicy().hasHeightForWidth())
                self.logoutBtn.setSizePolicy(sizePolicy)
                self.logoutBtn.setLayoutDirection(QtCore.Qt.LeftToRight)
                self.logoutBtn.setStyleSheet("")
                icon5 = QtGui.QIcon()
                icon5.addPixmap(QtGui.QPixmap(".\\images/images/log-out.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                self.logoutBtn.setIcon(icon5)
                self.logoutBtn.setIconSize(QtCore.QSize(26, 26))
                self.logoutBtn.setObjectName("logoutBtn")
                self.verticalLayout_2.addWidget(self.logoutBtn)
                self.gridLayout_19.addWidget(self.farmeleftMenu, 0, 0, 1, 1)
                self.horizontalLayout.addWidget(self.fullleftMenu)
                self.fullrightMenu = QtWidgets.QFrame(self.MainPages)
                self.fullrightMenu.setStyleSheet("background-color: #272c36;")
                self.fullrightMenu.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.fullrightMenu.setFrameShadow(QtWidgets.QFrame.Raised)
                self.fullrightMenu.setObjectName("fullrightMenu")
                self.verticalLayout = QtWidgets.QVBoxLayout(self.fullrightMenu)
                self.verticalLayout.setContentsMargins(0, 0, 0, 0)
                self.verticalLayout.setSpacing(0)
                self.verticalLayout.setObjectName("verticalLayout")
                self.rightTitle = QtWidgets.QFrame(self.fullrightMenu)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.rightTitle.sizePolicy().hasHeightForWidth())
                self.rightTitle.setSizePolicy(sizePolicy)
                self.rightTitle.setStyleSheet("*{\n"
        "padding:3 0;\n"
        "background-color:#343b48;\n"
        "}\n"
        "\n"
        "QFrame #rightTitle{\n"
        "\n"
        "border-bottom: 2px solid #3c4454;\n"
        "\n"
        "}\n"
        "\n"
        "\n"
        "")
                self.rightTitle.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.rightTitle.setFrameShadow(QtWidgets.QFrame.Raised)
                self.rightTitle.setObjectName("rightTitle")
                self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.rightTitle)
                self.horizontalLayout_3.setContentsMargins(3, 0, 0, 0)
                self.horizontalLayout_3.setSpacing(0)
                self.horizontalLayout_3.setObjectName("horizontalLayout_3")
                self.leftTitle = QtWidgets.QFrame(self.rightTitle)
                self.leftTitle.setStyleSheet("")
                self.leftTitle.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.leftTitle.setFrameShadow(QtWidgets.QFrame.Raised)
                self.leftTitle.setObjectName("leftTitle")
                self.gridLayout_3 = QtWidgets.QGridLayout(self.leftTitle)
                self.gridLayout_3.setContentsMargins(5, 0, 0, 0)
                self.gridLayout_3.setSpacing(0)
                self.gridLayout_3.setObjectName("gridLayout_3")
                self.btn_title = QtWidgets.QPushButton(self.leftTitle)
                self.btn_title.setStyleSheet("font:bold;")
                self.btn_title.setIcon(icon2)
                self.btn_title.setIconSize(QtCore.QSize(24, 24))
                self.btn_title.setObjectName("btn_title")
                self.gridLayout_3.addWidget(self.btn_title, 0, 0, 1, 1, QtCore.Qt.AlignLeft)
                self.horizontalLayout_3.addWidget(self.leftTitle)
                self.rightButtons = QtWidgets.QFrame(self.rightTitle)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.rightButtons.sizePolicy().hasHeightForWidth())
                self.rightButtons.setSizePolicy(sizePolicy)
                self.rightButtons.setStyleSheet("\n"
        "QPushButton:hover {\n"
        "    background-color: #3c4454;\n"
        "    border-radius:5px;\n"
        "\n"
        "\n"
        "}\n"
        "QPushButton:pressed { \n"
        "    background-color: #2c313c;\n"
        "     border-radius:5px;\n"
        "}\n"
        "\n"
        "\n"
        "\n"
        "")
                self.rightButtons.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.rightButtons.setFrameShadow(QtWidgets.QFrame.Raised)
                self.rightButtons.setObjectName("rightButtons")
                self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.rightButtons)
                self.horizontalLayout_2.setContentsMargins(0, 0, 6, 0)
                self.horizontalLayout_2.setSpacing(3)
                self.horizontalLayout_2.setObjectName("horizontalLayout_2")
                self.settingAppBtn = QtWidgets.QPushButton(self.rightButtons)
                self.settingAppBtn.setText("")
                icon6 = QtGui.QIcon()
                icon6.addPixmap(QtGui.QPixmap(".\\images/icons/icon_settings.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                self.settingAppBtn.setIcon(icon6)
                self.settingAppBtn.setIconSize(QtCore.QSize(28, 24))
                self.settingAppBtn.setObjectName("settingAppBtn")
                self.horizontalLayout_2.addWidget(self.settingAppBtn)
                self.minimizeAppBtn = QtWidgets.QPushButton(self.rightButtons)
                self.minimizeAppBtn.setStyleSheet("")
                self.minimizeAppBtn.setText("")
                icon7 = QtGui.QIcon()
                icon7.addPixmap(QtGui.QPixmap(".\\images/icons/icon_minimize.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                self.minimizeAppBtn.setIcon(icon7)
                self.minimizeAppBtn.setIconSize(QtCore.QSize(28, 24))
                self.minimizeAppBtn.setObjectName("minimizeAppBtn")
                self.horizontalLayout_2.addWidget(self.minimizeAppBtn)
                self.maximizeRestoreAppBtn = QtWidgets.QPushButton(self.rightButtons)
                self.maximizeRestoreAppBtn.setStyleSheet("")
                self.maximizeRestoreAppBtn.setText("")
                icon8 = QtGui.QIcon()
                icon8.addPixmap(QtGui.QPixmap(".\\images/icons/icon_maximize.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                self.maximizeRestoreAppBtn.setIcon(icon8)
                self.maximizeRestoreAppBtn.setIconSize(QtCore.QSize(28, 24))
                self.maximizeRestoreAppBtn.setObjectName("maximizeRestoreAppBtn")
                self.horizontalLayout_2.addWidget(self.maximizeRestoreAppBtn)
                self.closeAppBtn = QtWidgets.QPushButton(self.rightButtons)
                self.closeAppBtn.setStyleSheet("\n"
        "QPushButton:pressed { \n"
        "background-color: #ff5555;\n"
        " border-style: solid; border-radius: 4px; \n"
        "}\n"
        "")
                self.closeAppBtn.setText("")
                icon9 = QtGui.QIcon()
                icon9.addPixmap(QtGui.QPixmap(".\\images/icons/icon_close.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                self.closeAppBtn.setIcon(icon9)
                self.closeAppBtn.setIconSize(QtCore.QSize(28, 24))
                self.closeAppBtn.setObjectName("closeAppBtn")
                self.horizontalLayout_2.addWidget(self.closeAppBtn)
                self.horizontalLayout_3.addWidget(self.rightButtons)
                self.verticalLayout.addWidget(self.rightTitle)
                self.fullrightPages = QtWidgets.QFrame(self.fullrightMenu)
                self.fullrightPages.setStyleSheet("")
                self.fullrightPages.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.fullrightPages.setFrameShadow(QtWidgets.QFrame.Raised)
                self.fullrightPages.setObjectName("fullrightPages")
                self.gridLayout_4 = QtWidgets.QGridLayout(self.fullrightPages)
                self.gridLayout_4.setContentsMargins(0, 0, 0, 0)
                self.gridLayout_4.setSpacing(0)
                self.gridLayout_4.setObjectName("gridLayout_4")
                self.stackedWidget = QtWidgets.QStackedWidget(self.fullrightPages)
                self.stackedWidget.setStyleSheet("")
                self.stackedWidget.setLineWidth(0)
                self.stackedWidget.setObjectName("stackedWidget")
                self.stack_tiktok = QtWidgets.QWidget()
                self.stack_tiktok.setObjectName("stack_tiktok")
                self.gridLayout_13 = QtWidgets.QGridLayout(self.stack_tiktok)
                self.gridLayout_13.setContentsMargins(3, 0, 3, 3)
                self.gridLayout_13.setHorizontalSpacing(3)
                self.gridLayout_13.setVerticalSpacing(6)
                self.gridLayout_13.setObjectName("gridLayout_13")
                self.fullPagesTikTok = QtWidgets.QFrame(self.stack_tiktok)
                self.fullPagesTikTok.setStyleSheet("")
                self.fullPagesTikTok.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.fullPagesTikTok.setFrameShadow(QtWidgets.QFrame.Raised)
                self.fullPagesTikTok.setObjectName("fullPagesTikTok")
                self.horizontalLayout_13 = QtWidgets.QHBoxLayout(self.fullPagesTikTok)
                self.horizontalLayout_13.setContentsMargins(0, 0, 0, 0)
                self.horizontalLayout_13.setSpacing(0)
                self.horizontalLayout_13.setObjectName("horizontalLayout_13")
                self.leftPages = QtWidgets.QFrame(self.fullPagesTikTok)
                self.leftPages.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.leftPages.setFrameShadow(QtWidgets.QFrame.Raised)
                self.leftPages.setObjectName("leftPages")
                self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.leftPages)
                self.verticalLayout_6.setContentsMargins(0, 0, 0, 0)
                self.verticalLayout_6.setSpacing(0)
                self.verticalLayout_6.setObjectName("verticalLayout_6")
                self.PagesTikTok = QtWidgets.QFrame(self.leftPages)
                self.PagesTikTok.setStyleSheet("")
                self.PagesTikTok.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.PagesTikTok.setFrameShadow(QtWidgets.QFrame.Raised)
                self.PagesTikTok.setObjectName("PagesTikTok")
                self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.PagesTikTok)
                self.verticalLayout_7.setContentsMargins(3, 3, 3, 3)
                self.verticalLayout_7.setSpacing(0)
                self.verticalLayout_7.setObjectName("verticalLayout_7")
                self.frame_14 = QtWidgets.QFrame(self.PagesTikTok)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.frame_14.sizePolicy().hasHeightForWidth())
                self.frame_14.setSizePolicy(sizePolicy)
                self.frame_14.setStyleSheet("QComboBox{\n"
        "color: rgba(118,120,120,255);\n"
        "}")
                self.frame_14.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.frame_14.setFrameShadow(QtWidgets.QFrame.Raised)
                self.frame_14.setObjectName("frame_14")
                self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.frame_14)
                self.horizontalLayout_8.setContentsMargins(0, 0, 0, 6)
                self.horizontalLayout_8.setSpacing(0)
                self.horizontalLayout_8.setObjectName("horizontalLayout_8")
                self.leftPagesGRTIK = QtWidgets.QFrame(self.frame_14)
                self.leftPagesGRTIK.setStyleSheet("")
                self.leftPagesGRTIK.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.leftPagesGRTIK.setFrameShadow(QtWidgets.QFrame.Raised)
                self.leftPagesGRTIK.setObjectName("leftPagesGRTIK")
                self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.leftPagesGRTIK)
                self.horizontalLayout_12.setContentsMargins(0, 2, 4, 2)
                self.horizontalLayout_12.setSpacing(6)
                self.horizontalLayout_12.setObjectName("horizontalLayout_12")
                self.groupBox_6 = QtWidgets.QGroupBox(self.leftPagesGRTIK)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.groupBox_6.sizePolicy().hasHeightForWidth())
                self.groupBox_6.setSizePolicy(sizePolicy)
                self.groupBox_6.setMaximumSize(QtCore.QSize(********, ********))
                self.groupBox_6.setStyleSheet("")
                self.groupBox_6.setObjectName("groupBox_6")
                self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.groupBox_6)
                self.horizontalLayout_7.setContentsMargins(3, 9, 3, 5)
                self.horizontalLayout_7.setSpacing(3)
                self.horizontalLayout_7.setObjectName("horizontalLayout_7")
                self.label_6 = QtWidgets.QLabel(self.groupBox_6)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.label_6.sizePolicy().hasHeightForWidth())
                self.label_6.setSizePolicy(sizePolicy)
                self.label_6.setMaximumSize(QtCore.QSize(********, ********))
                self.label_6.setObjectName("label_6")
                self.horizontalLayout_7.addWidget(self.label_6)
                self.folderCbox = QtWidgets.QComboBox(self.groupBox_6)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.folderCbox.sizePolicy().hasHeightForWidth())
                self.folderCbox.setSizePolicy(sizePolicy)
                self.folderCbox.setStyleSheet("")
                self.folderCbox.setObjectName("folderCbox")
                self.folderCbox.addItem("")
                self.horizontalLayout_7.addWidget(self.folderCbox)
                self.addFolderBtn = QtWidgets.QPushButton(self.groupBox_6)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.addFolderBtn.sizePolicy().hasHeightForWidth())
                self.addFolderBtn.setSizePolicy(sizePolicy)
                self.addFolderBtn.setText("")
                icon10 = QtGui.QIcon()
                icon10.addPixmap(QtGui.QPixmap(".\\images/images/plus.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                self.addFolderBtn.setIcon(icon10)
                self.addFolderBtn.setIconSize(QtCore.QSize(24, 24))
                self.addFolderBtn.setObjectName("addFolderBtn")
                self.horizontalLayout_7.addWidget(self.addFolderBtn)
                self.editFolderBtn = QtWidgets.QPushButton(self.groupBox_6)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.editFolderBtn.sizePolicy().hasHeightForWidth())
                self.editFolderBtn.setSizePolicy(sizePolicy)
                self.editFolderBtn.setText("")
                icon11 = QtGui.QIcon()
                icon11.addPixmap(QtGui.QPixmap(".\\images/images/edit.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                self.editFolderBtn.setIcon(icon11)
                self.editFolderBtn.setIconSize(QtCore.QSize(24, 24))
                self.editFolderBtn.setObjectName("editFolderBtn")
                self.horizontalLayout_7.addWidget(self.editFolderBtn)
                self.reFolderBtn = QtWidgets.QPushButton(self.groupBox_6)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.reFolderBtn.sizePolicy().hasHeightForWidth())
                self.reFolderBtn.setSizePolicy(sizePolicy)
                self.reFolderBtn.setText("")
                icon12 = QtGui.QIcon()
                icon12.addPixmap(QtGui.QPixmap(".\\images/images/minimize.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                self.reFolderBtn.setIcon(icon12)
                self.reFolderBtn.setIconSize(QtCore.QSize(32, 32))
                self.reFolderBtn.setObjectName("reFolderBtn")
                self.horizontalLayout_7.addWidget(self.reFolderBtn)
                self.horizontalLayout_12.addWidget(self.groupBox_6)
                self.horizontalLayout_8.addWidget(self.leftPagesGRTIK)
                self.groupBox_5 = QtWidgets.QGroupBox(self.frame_14)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.groupBox_5.sizePolicy().hasHeightForWidth())
                self.groupBox_5.setSizePolicy(sizePolicy)
                self.groupBox_5.setObjectName("groupBox_5")
                self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.groupBox_5)
                self.horizontalLayout_9.setContentsMargins(3, 9, 3, 5)
                self.horizontalLayout_9.setSpacing(3)
                self.horizontalLayout_9.setObjectName("horizontalLayout_9")
                self.searchTableTik = QtWidgets.QLineEdit(self.groupBox_5)
                self.searchTableTik.setStyleSheet("padding: 3px; font-size: 14px;width:150px;")
                self.searchTableTik.setObjectName("searchTableTik")
                self.horizontalLayout_9.addWidget(self.searchTableTik)
                self.inputAccountsBtn = QtWidgets.QPushButton(self.groupBox_5)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.inputAccountsBtn.sizePolicy().hasHeightForWidth())
                self.inputAccountsBtn.setSizePolicy(sizePolicy)
                self.inputAccountsBtn.setMaximumSize(QtCore.QSize(********, ********))
                self.inputAccountsBtn.setStyleSheet("")
                self.inputAccountsBtn.setText("")
                self.inputAccountsBtn.setIcon(icon10)
                self.inputAccountsBtn.setIconSize(QtCore.QSize(24, 24))
                self.inputAccountsBtn.setObjectName("inputAccountsBtn")
                self.horizontalLayout_9.addWidget(self.inputAccountsBtn)
                self.label_7 = QtWidgets.QLabel(self.groupBox_5)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.label_7.sizePolicy().hasHeightForWidth())
                self.label_7.setSizePolicy(sizePolicy)
                self.label_7.setObjectName("label_7")
                self.horizontalLayout_9.addWidget(self.label_7)
                self.statusCbox = QtWidgets.QComboBox(self.groupBox_5)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.statusCbox.sizePolicy().hasHeightForWidth())
                self.statusCbox.setSizePolicy(sizePolicy)
                self.statusCbox.setStyleSheet("")
                self.statusCbox.setObjectName("statusCbox")
                self.statusCbox.addItem("")
                self.horizontalLayout_9.addWidget(self.statusCbox)
                self.horizontalLayout_8.addWidget(self.groupBox_5)
                spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
                self.horizontalLayout_8.addItem(spacerItem1)
                self.rightPagesGRTIK = QtWidgets.QFrame(self.frame_14)
                self.rightPagesGRTIK.setStyleSheet("QPushButton{\n"
        "background-color: #1b1e23;\n"
        "border-radius:8px;\n"
        "padding:12 25;\n"
        "text-align:left;\n"
        "}\n"
        "QPushButton:hover {\n"
        "    background-color: #1e2229;\n"
        "    border-radius:8px;\n"
        "\n"
        "}\n"
        "QPushButton:pressed { \n"
        "    background-color: #2c313c;\n"
        "     border-radius:8px;\n"
        "}\n"
        "\n"
        "\n"
        "\n"
        "")
                self.rightPagesGRTIK.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.rightPagesGRTIK.setFrameShadow(QtWidgets.QFrame.Raised)
                self.rightPagesGRTIK.setObjectName("rightPagesGRTIK")
                self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.rightPagesGRTIK)
                self.horizontalLayout_11.setObjectName("horizontalLayout_11")
                self.horizontalLayout_8.addWidget(self.rightPagesGRTIK)
                self.verticalLayout_7.addWidget(self.frame_14)
                self.lblStatus = QtWidgets.QLabel(self.PagesTikTok)
                self.lblStatus.setStyleSheet("border-top:2px solid #5fbc5a;\n"
        "background-color: #111111;\n"
        "padding:5 5 5 5;")
                self.lblStatus.setObjectName("lblStatus")
                self.verticalLayout_7.addWidget(self.lblStatus)
                self.frame_21 = QtWidgets.QFrame(self.PagesTikTok)
                self.frame_21.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.frame_21.setFrameShadow(QtWidgets.QFrame.Raised)
                self.frame_21.setObjectName("frame_21")
                self.gridLayout_18 = QtWidgets.QGridLayout(self.frame_21)
                self.gridLayout_18.setContentsMargins(0, 0, 0, 0)
                self.gridLayout_18.setSpacing(0)
                self.gridLayout_18.setObjectName("gridLayout_18")
                self.splitter = QtWidgets.QSplitter(self.frame_21)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.splitter.sizePolicy().hasHeightForWidth())
                self.splitter.setSizePolicy(sizePolicy)
                self.splitter.setStyleSheet("")
                self.splitter.setOrientation(QtCore.Qt.Vertical)
                self.splitter.setOpaqueResize(True)
                self.splitter.setHandleWidth(2)
                self.splitter.setChildrenCollapsible(True)
                self.splitter.setObjectName("splitter")
                self.tableWidget = QtWidgets.QTableWidget(self.splitter)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.tableWidget.sizePolicy().hasHeightForWidth())
                self.tableWidget.setSizePolicy(sizePolicy)
                self.tableWidget.setStyleSheet("QTableWidget {\n"
        "    background-color: #343b48;\n"
        "    color: rgb(138, 149, 170);\n"
        "    padding: 5; \n"
        "\n"
        "}\n"
        "\n"
        "QTableWidget::item {\n"
        "    background-color: #343b48;\n"
        "    border-color: rgb(44, 49, 60);\n"
        "    padding-left: 5px;\n"
        "    padding-right: 5px;\n"
        "    gridline-color: rgb(44, 49, 60);\n"
        "\n"
        "}\n"
        "QTableWidget::item:selected{\n"
        "    background-color: rgb(189, 147, 249);\n"
        "}\n"
        "QTableWidget::horizontalHeader {    \n"
        "    background-color: rgb(33, 37, 43);\n"
        "}\n"
        "\n"
        "QHeaderView {\n"
        "\n"
        "    border-top-left-radius: 5px;\n"
        "    border-top-right-radius: 5px;\n"
        "    background-color: #1b1e23;\n"
        "    color: rgb(138, 149, 170);\n"
        "    font:bold;\n"
        "    font-family: \'Montserrat\', sans-serif;\n"
        "\n"
        "    \n"
        "}\n"
        "\n"
        "QHeaderView::section {\n"
        "    padding:5 0;\n"
        "    background-color: #191818;\n"
        "    border-style: none;\n"
        "    padding-left:5px;"
        "}\n"
        "\n"
        "\n"
        "")
                self.tableWidget.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
                self.tableWidget.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOn)
                self.tableWidget.setGridStyle(QtCore.Qt.SolidLine)
                self.tableWidget.setObjectName("tableWidget")
                self.tableWidget.setColumnCount(15)
                self.tableWidget.setRowCount(0)
                for t in range(15):
                    item = QtWidgets.QTableWidgetItem()
                    self.tableWidget.setHorizontalHeaderItem(t, item)
                self.gridLayout_18.addWidget(self.splitter, 0, 0, 1, 1)
                self.verticalLayout_7.addWidget(self.frame_21)
                self.verticalLayout_6.addWidget(self.PagesTikTok)
                self.horizontalLayout_13.addWidget(self.leftPages)
                self.gridLayout_13.addWidget(self.fullPagesTikTok, 0, 0, 1, 1)
                self.stackedWidget.addWidget(self.stack_tiktok)
                self.stack_chrome = QtWidgets.QWidget()
                self.stack_chrome.setObjectName("stack_chrome")
                self.gridLayout_2 = QtWidgets.QGridLayout(self.stack_chrome)
                self.gridLayout_2.setObjectName("gridLayout_2")
                self.frame_3 = QtWidgets.QFrame(self.stack_chrome)
                self.frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
                self.frame_3.setObjectName("frame_3")
                self.gridLayout_5 = QtWidgets.QGridLayout(self.frame_3)
                self.gridLayout_5.setContentsMargins(0, 0, 0, 0)
                self.gridLayout_5.setHorizontalSpacing(0)
                self.gridLayout_5.setObjectName("gridLayout_5")
                self.tableWidgetChrome = QtWidgets.QTableWidget(self.frame_3)
                self.tableWidgetChrome.setStyleSheet("QTableWidget {\n"
        "    background-color: #343b48;\n"
        "    color: rgb(138, 149, 170);\n"
        "    padding: 5; \n"
        "\n"
        "}\n"
        "\n"
        "QTableWidget::item {\n"
        "    background-color: #343b48;\n"
        "    border-color: rgb(44, 49, 60);\n"
        "    padding-left: 5px;\n"
        "    padding-right: 5px;\n"
        "    gridline-color: rgb(44, 49, 60);\n"
        "\n"
        "}\n"
        "QTableWidget::item:selected{\n"
        "    background-color: rgb(189, 147, 249);\n"
        "}\n"
        "QTableWidget::horizontalHeader {    \n"
        "    background-color: rgb(33, 37, 43);\n"
        "}\n"
        "\n"
        "QHeaderView {\n"
        "\n"
        "    border-top-left-radius: 5px;\n"
        "    border-top-right-radius: 5px;\n"
        "    background-color: #1b1e23;\n"
        "    color: rgb(138, 149, 170);\n"
        "    font:bold;\n"
        "    font-family: \'Montserrat\', sans-serif;\n"
        "\n"
        "    \n"
        "}\n"
        "\n"
        "QHeaderView::section {\n"
        "    padding:5 0;\n"
        "    background-color: #191818;\n"
        "    border-style: none;\n"
        "    padding-left:5px;"
        "}\n"
        "\n"
        "\n"
        "")
                self.tableWidgetChrome.setObjectName("tableWidgetChrome")
                self.tableWidgetChrome.setColumnCount(4)
                self.tableWidgetChrome.setRowCount(0)
                item = QtWidgets.QTableWidgetItem()
                self.tableWidgetChrome.setHorizontalHeaderItem(0, item)
                item = QtWidgets.QTableWidgetItem()
                self.tableWidgetChrome.setHorizontalHeaderItem(1, item)
                item = QtWidgets.QTableWidgetItem()
                self.tableWidgetChrome.setHorizontalHeaderItem(2, item)
                item = QtWidgets.QTableWidgetItem()
                self.tableWidgetChrome.setHorizontalHeaderItem(3, item)
                self.gridLayout_5.addWidget(self.tableWidgetChrome, 0, 0, 1, 1)
                self.gridLayout_2.addWidget(self.frame_3, 0, 0, 1, 1)
                self.stackedWidget.addWidget(self.stack_chrome)
                self.gridLayout_4.addWidget(self.stackedWidget, 0, 0, 1, 1)
                self.bottom = QtWidgets.QFrame(self.fullrightPages)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.bottom.sizePolicy().hasHeightForWidth())
                self.bottom.setSizePolicy(sizePolicy)
                self.bottom.setStyleSheet("*{\n"
        "    background-color: #181818;\n"
        "    color:#5f8899;\n"
        "    height:30px;\n"
        "}\n"
        "\n"
        "")
                self.bottom.setFrameShape(QtWidgets.QFrame.StyledPanel)
                self.bottom.setFrameShadow(QtWidgets.QFrame.Raised)
                self.bottom.setObjectName("bottom")
                self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.bottom)
                self.horizontalLayout_10.setContentsMargins(5, 5, 5, 5)
                self.horizontalLayout_10.setSpacing(0)
                self.horizontalLayout_10.setObjectName("horizontalLayout_10")
                self.lblCoppyright = QtWidgets.QLabel(self.bottom)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.lblCoppyright.sizePolicy().hasHeightForWidth())
                self.lblCoppyright.setSizePolicy(sizePolicy)
                self.lblCoppyright.setObjectName("lblCoppyright")
                self.horizontalLayout_10.addWidget(self.lblCoppyright)
                self.lblPyTournes1 = QtWidgets.QLabel(self.bottom)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.lblPyTournes1.sizePolicy().hasHeightForWidth())
                self.lblPyTournes1.setSizePolicy(sizePolicy)
                self.lblPyTournes1.setStyleSheet("QLabel {\n"
        "color:#5fbc5a;\n"
        "font:bold;\n"
        "}\n"
        "QLabel:hover{\n"
        "    color:#ff0000;\n"
        "}\n"
        "")
                self.lblPyTournes1.setObjectName("lblPyTournes1")
                self.horizontalLayout_10.addWidget(self.lblPyTournes1)
                self.label_3 = QtWidgets.QLabel(self.bottom)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
                self.label_3.setSizePolicy(sizePolicy)
                self.label_3.setStyleSheet("")
                self.label_3.setObjectName("label_3")
                self.horizontalLayout_10.addWidget(self.label_3)
                self.lblPyTournes2 = QtWidgets.QLabel(self.bottom)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.lblPyTournes2.sizePolicy().hasHeightForWidth())
                self.lblPyTournes2.setSizePolicy(sizePolicy)
                self.lblPyTournes2.setStyleSheet("QLabel {\n"
        "color:#5fbc5a;\n"
        "font:bold;\n"
        "}\n"
        "QLabel:hover{\n"
        "    color:#ff0000;\n"
        "}\n"
        "")
                self.lblPyTournes2.setObjectName("lblPyTournes2")
                self.horizontalLayout_10.addWidget(self.lblPyTournes2)
                self.label_5 = QtWidgets.QLabel(self.bottom)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.label_5.sizePolicy().hasHeightForWidth())
                self.label_5.setSizePolicy(sizePolicy)
                self.label_5.setObjectName("label_5")
                self.horizontalLayout_10.addWidget(self.label_5)
                self.lblPyTournes3 = QtWidgets.QLabel(self.bottom)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.lblPyTournes3.sizePolicy().hasHeightForWidth())
                self.lblPyTournes3.setSizePolicy(sizePolicy)
                self.lblPyTournes3.setStyleSheet("QLabel {\n"
        "color:#5fbc5a;\n"
        "font:bold;\n"
        "}\n"
        "QLabel:hover{\n"
        "    color:#ff0000;\n"
        "}\n"
        "")
                self.lblPyTournes3.setObjectName("lblPyTournes3")
                self.horizontalLayout_10.addWidget(self.lblPyTournes3)
                self.label_9 = QtWidgets.QLabel(self.bottom)
                self.label_9.setObjectName("label_9")
                self.horizontalLayout_10.addWidget(self.label_9)
                self.label = QtWidgets.QLabel(self.bottom)
                self.label.setText("")
                self.label.setObjectName("label")
                self.horizontalLayout_10.addWidget(self.label)
                self.pushButton = QtWidgets.QPushButton(self.bottom)
                sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
                sizePolicy.setHorizontalStretch(0)
                sizePolicy.setVerticalStretch(0)
                sizePolicy.setHeightForWidth(self.pushButton.sizePolicy().hasHeightForWidth())
                self.pushButton.setSizePolicy(sizePolicy)
                self.pushButton.setText("")
                self.pushButton.setObjectName("pushButton")
                self.horizontalLayout_10.addWidget(self.pushButton)
                self.lblTimeoutSV = QtWidgets.QLabel(self.bottom)
                self.lblTimeoutSV.setObjectName("lblTimeoutSV")
                self.horizontalLayout_10.addWidget(self.lblTimeoutSV, 0, QtCore.Qt.AlignRight)
                self.gridLayout_4.addWidget(self.bottom, 1, 0, 1, 2)
                self.verticalLayout.addWidget(self.fullrightPages)
                self.horizontalLayout.addWidget(self.fullrightMenu)
                self.verticalLayout_31.addWidget(self.MainPages)
                APP.setCentralWidget(self.stylesheet)

                self.retranslateUi(APP)
                self.stackedWidget.setCurrentIndex(0)
                QtCore.QMetaObject.connectSlotsByName(APP)

    def retranslateUi(self, APP):
        _translate = QtCore.QCoreApplication.translate
        APP.setWindowTitle(_translate("APP", "PyTournes - APP"))
        self.leftMenuBtn.setText(_translate("APP", "Hide Menu"))
        self.btn_chrome.setText(_translate("APP", "Chrome"))
        self.btn_tiktok.setText(_translate("APP", "TikTok     "))
        self.btn_notify.setText(_translate("APP", "Notify"))
        self.btn_help.setText(_translate("APP", "Support"))
        self.logoutBtn.setText(_translate("APP", "Logout"))
        self.btn_title.setText(_translate("APP", "Auto Tik Tok"))
        self.groupBox_6.setTitle(_translate("APP", "Folder management"))
        self.label_6.setText(_translate("APP", "Thư mục:"))
        self.folderCbox.setItemText(0, _translate("APP", "[ All Accounts ]"))
        self.groupBox_5.setTitle(_translate("APP", "Account management"))
        self.searchTableTik.setPlaceholderText(_translate("APP", "Search"))
        self.label_7.setText(_translate("APP", "Lọc:"))
        self.statusCbox.setItemText(0, _translate("APP", "[ All Status ]"))
        self.lblStatus.setText(_translate("APP", "<html><head/><body><p><span style=\" color:#5f8899;\">Blacken: </span><span style=\" font-weight:600; color:#5fbc5a;\">0 </span><span style=\" color:#5f8899;\">| Select: </span><span style=\" font-weight:600; color:#5fbc5a;\">0 </span><span style=\" color:#5f8899;\">| Selected: 0 | All: </span><span style=\" font-weight:600; color:#5fbc5a;\">0</span><span style=\" color:#5f8899;\"> | Coin: </span><span style=\" font-weight:600; color:#5fbc5a;\">30.000 </span><span style=\" color:#5f8899;\">| Coin received: </span><span style=\" font-weight:600; color:#5fbc5a;\">30.000 </span><span style=\" color:#5f8899;\">| Device Online: </span><span style=\" font-weight:600; color:#5fbc5a;\">3</span><span style=\" color:#5f8899;\">/</span><span style=\" font-weight:600; color:#5fbc5a;\">3 </span><span style=\" color:#5f8899;\">| Device ID: </span><span style=\" font-weight:600; color:#5fbc5a;\">4c19ef5af3915feb5d442687786301f2 </span><span style=\" color:#5f8899;\">| Your IP: </span><span style=\" font-weight:600; color:#5fbc5a;\">***********</span></p></body></html>"))
        self.tableWidget.setSortingEnabled(False)
      
        item = self.tableWidgetChrome.horizontalHeaderItem(0)
        item.setText(_translate("APP", "STT"))
        item = self.tableWidgetChrome.horizontalHeaderItem(1)
        item.setText(_translate("APP", "Name"))
        item = self.tableWidgetChrome.horizontalHeaderItem(2)
        item.setText(_translate("APP", "Create"))
        item = self.tableWidgetChrome.horizontalHeaderItem(3)
        item.setText(_translate("APP", "Status"))
        self.lblCoppyright.setText(_translate("APP", "Powered by "))
        self.lblPyTournes1.setText(F"{SHELLVERSION}")
        self.label_3.setText(_translate("APP", "- Developed by "))
        self.lblPyTournes2.setText(_translate("APP", "PyTournes"))
        self.label_5.setText(_translate("APP", ". Coppyright © 2023 "))
        self.lblPyTournes3.setText(_translate("APP", "PyTournes"))
        self.label_9.setText(_translate("APP", ". All rights reserved"))
        self.lblTimeoutSV.setText(_translate("APP", "<html><head/><body><p><span style=\" color:#5f8899;\">Processed in </span><span style=\" font-weight:600; color:#5fbc5a;\">0.017 </span><span style=\" color:#5f8899;\">second(s)</span></p></body></html>"))


        self.tableWidgetText = [
        "STT", "UID", "Password", "Cookie", "Name", "UserCoin", "PassCoin", "XuHT", "XuThem","Tỷ lệ",
        "Job/Cache", "Profile ID","Proxy", "Trạng Thái", "Action"
        ]

        header_width =[
            20,60,68,50,50,60,68,70,70,100,75,80,50,800,100]        
        
        for t, r, n in zip(range(15),self.tableWidgetText,header_width):
            item = self.tableWidget.horizontalHeaderItem(t)
            item.setText(r)
            self.tableWidget.setColumnWidth(t, n)
            item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        self.tableWidget.horizontalHeader().resizeSection(13, 500)  # Đặt chiều rộng mặc định cho cột 13 là 800
        self.tableWidget.horizontalHeader().setSectionResizeMode(13, QHeaderView.Interactive)  # Chế độ co giãn tự động cho cột 13

        # Chỉnh kích thước mặc định cho cột 12 và chế độ Interactive
        self.tableWidget.horizontalHeader().setSectionResizeMode(14, QHeaderView.Interactive)
        self.tableWidget.horizontalHeader().resizeSection(14, 100)  # Đặt kích thước mặc định cho cột 12 (ở đây là 50)
        
        self.tableWidget.verticalHeader().setVisible(False)
        self.tableWidget.setSelectionBehavior(QtWidgets.QTableView.SelectRows)
        
        
        self.tableWidget.setEditTriggers(QTableWidget.NoEditTriggers)
        for x in [2, 6, 15]:
            delegate = PasswordDelegate(self.tableWidget)
            delegate.setColumn(x)
            self.tableWidget.setItemDelegate(delegate)
            
        
        self.tableWidgetChrome.verticalHeader().setVisible(False)
        self.tableWidgetChrome.setSelectionBehavior(QtWidgets.QTableView.SelectRows)
        self.tableWidgetChrome.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)
        
        self.tableWidgetChromeText = [
        "STT", "Name", "Create", "Status"
        ]

        header_width =[20,250,300,300]        
        
        for t, r, n in zip(range(4),self.tableWidgetChromeText,header_width):
            item = self.tableWidgetChrome.horizontalHeaderItem(t)
            item.setText(r)
            self.tableWidgetChrome.setColumnWidth(t, n)
            item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        self.APP = APP
        
        # Tạo ra 4 grip để resize
        for i in range(4):grip = QSizeGrip(self.stylesheet);grip.resize(self.gripSize, self.gripSize);self.grips.append(grip)
        self.APP.moveEvent = self.APP
        self.APP.mousePressEvent = self.mousePressEvent
        self.APP.mouseMoveEvent = self.mouseMoveEvent
        self.APP.mouseDoubleClickEvent = self.mouseDoubleClickEvent

        # Chức năng của rightButtons
        self.minimizeAppBtn.clicked.connect(lambda: self.APP.showMinimized())
        self.maximizeRestoreAppBtn.clicked.connect(self.toggle_maximized)
        self.closeAppBtn.clicked.connect(self.closeAPP)
        self.settingAppBtn.clicked.connect(lambda: self.showUI('settings'))
        # - - - - - - - - - - - - - - - - - - - - - - - - - - - 

        # Chức năng liên quan đến sider bar
        
        # Gọi lớp kế thừa của Set ICON
        self.iconClass = setIcon(self)
        # - - - - - - - - - - - - - - - - - - - - - - - - - - - 

        self.animationSideBar = QPropertyAnimation(self.fullleftMenu, b"maximumWidth")
        self.animationSideBar.setDuration(100)  # Thời gian animation (ms)
        self.animationSideBar.setEasingCurve(QEasingCurve.Linear)
        # - - - - - - - - - - - - - - - - - - - - - - - - - - - 
        
        # Dictionary contains information about symbols and corresponding titles
        self.icon_info = {
            'chrome': ("Profile Chrome", "google.png"),
            'tiktok': ("Auto Tik Tok", "tik-tok (1).png"),
           
        }
        # - - - - - - - - - - - - - - - - - - - - - - - - - - - 
        self.leftMenuBtn.clicked.connect(lambda:self.sideBarClicked('menu'))
        self.btn_chrome.clicked.connect(lambda:self.sideBarClicked('chrome'))
        self.btn_tiktok.clicked.connect(lambda:self.sideBarClicked('tiktok'))
        self.logoutBtn.clicked.connect(lambda:self.sideBarClicked('logout'))
        self.btn_notify.clicked.connect(lambda: self.uiFuncions.MessageBoxShow(text=f'{requests.get("https://pytournes.io.vn/server/pages/api/version_autotiktok.html").content.decode("utf-8")}'))

        # - - - - - - - - - - - - - - - - - - - - - - - - - - - 

        # Gọi lớp kế thừa của chức năng liên quan đến Action
        self.actionTable = ActionTable(self)

        # Load Data Table
        self.load_data = LoadData(self)
        self.load_data.editCellByColumnName.connect(self.editCellByColumnName)
        self.load_data.EnableStart.connect(self.actionTable.EnableStart)
        
        self.load_data_chrome = LoadDataChrome(self)
        self.load_data_chrome.setItem.connect(self.editRow)
        # self.load_data_chrome.start()

        # Gọi lớp kế thừa của chức năng liên quan đển Menu
        self.tableWidget.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.tableWidget.customContextMenuRequested.connect(self.menuTikTok)
        
        self.tableWidgetChrome.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.tableWidgetChrome.customContextMenuRequested.connect(self.menuChrome)
        self.tableWidget.itemSelectionChanged.connect(self.tableSelected)

        # - - - - - - - - - - - - - - - - - - - - - - - - - - - 

        # Gọi lớp kế thừa của các chức năng liên quan đến Folder, File
        self.loadFolder()
        self.addFolderBtn.clicked.connect(self.addNewFolder)
        self.editFolderBtn.clicked.connect(self.editFolder)
        self.reFolderBtn.clicked.connect(self.deleteFolder)
        self.folderCbox.currentIndexChanged.connect(self.loadDataFile)
        self.inputAccountsBtn.clicked.connect(lambda: self.showUI('import'))
        # - - - - - - - - - - - - - - - - - - - - - - - - - - - 

        # Chức năng của titleBottom
        labels = [self.lblPyTournes1, self.lblPyTournes2, self.lblPyTournes3]
        for label in labels:label.mousePressEvent = self.openURL
        # - - - - - - - - - - - - - - - - - - - - - - - - - - -
        self.DId = get_id_win()
        self.Ip = get_ip_address()
        self.statusBar()

        # self.ChromeViewer()

    # def ChromeViewer(self):
    #     from src.pages.ui_viewer import Ui_Viewer
    #     try: self.view 
    #     except:
    #         self.uiv = QtWidgets.QWidget()
    #         self.view = Ui_Viewer()
    #         self.view.setupUi(self.uiv)
    #         self.uiv.show()

    def showUI(self, type):
        from src.pages.ui_import import Ui_Import
        from src.pages.ui_settings import Ui_Settings
        try:
            self.main = QtWidgets.QMainWindow()
            if type == 'settings':
                self.ui = Ui_Settings()
            if type == 'import':
                if self.folderCbox.currentText() != '[ All Accounts ]':
                    self.ui = Ui_Import()
                    self.ui.setupUi(self.main,self.folderCbox.currentText())
                    self.main.show()   
                    return
                else:self.uiFuncions.MessageBoxShow(text='Unable to import data into Folder [ All Accounts ]');return
    
            self.ui.setupUi(self.main)
            self.main.show()  
        except Exception as e:
            print(e)

    def openURL(self, event):
        QDesktopServices.openUrl(QUrl("https://zalo.me/**********"))

    """Chức năng di chuyển zoom in zoom out APP"""
    def updateGripPositions(self):
        self.grips[0].move(0, 0) 
        self.grips[1].move(self.APP.width() - self.gripSize, 0) 
        self.grips[2].move(0, self.APP.height() - self.gripSize)  
        self.grips[3].move(self.APP.width() - self.gripSize, self.APP.height() - self.gripSize)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self._old_pos = event.pos()
            
    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            self._old_pos = None
            
    def mouseMoveEvent(self, event):
        if not self._old_pos:
            return
        delta = event.pos() - self._old_pos
        self.APP.move(self.APP.pos() + delta)
    
    def mouseDoubleClickEvent(self, event):
        if event.button() == Qt.LeftButton:self.toggle_maximized()

    """Chức năng của sideBar"""
    def setIconTitLe(self, type):
        """
        Đặt biểu tượng và tiêu đề cho nút btn_title dựa trên loại type được truyền vào.
        """
        if type in self.icon_info:
            text, icon_path = self.icon_info[type]
            self.btn_title.setText(text)
            self.iconClass.setIcon(self.btn_title, icon_path)
            
    def setButtonStyle(self, type):
        """
        Đặt kiểu cho các nút trong thanh bên dựa trên loại type được truyền vào.
        """
        button_list = [
            self.btn_chrome,
            self.btn_tiktok,
        ]
        
        for button in button_list:
            if type in button.objectName() :
                button.setStyleSheet("border-left:3px solid rgb(255, 255, 255);background-color: #2c313c;font:bold; ")
            else:
                button.setStyleSheet("")
                
    def sideBarClicked(self, type):
        """
        Xử lý sự kiện khi một nút trong thanh bên được nhấp.
        """
        if type == "menu":
            try:
                if self.stackCheck['stackInput'] == False:
                    # Kiểm tra xem có ít nhất một phần tử khác 'stackInput' có giá trị là True hay không
                    true_stack = next((key for key, value in self.stackCheck.items() if key != 'stackInput' and value), None)
                    if true_stack:
                        # tìm object dựa vào "true_stack" -> stackInput
                        found_object = self.findChild(QWidget, true_stack) # -> self.stackname
                        self.rightbtnFeatures.settingsClicked(found_object) # gọi tới class để đóng settings bên phải lại
                    # - - - - - - - - - - - - - - - - - - - - - - - - - - - 

                    self.iconClass.setIcon(self.leftMenuBtn, 'left-arrow.png')
                    self.stackCheck = {key: key == "stackInput" for key in self.stackCheck}
                    self.target_width = 170 
                else:
                    self.iconClass.setIcon(self.leftMenuBtn, 'menuLeft.png')
                    self.stackCheck.update( {"stackInput":False})
                    self.target_width = 60  
                self.animationSideBar.setStartValue(self.fullleftMenu.maximumWidth())
                self.animationSideBar.setEndValue(self.target_width)
                self.animationSideBar.start()
            except: pass
        if type == "logout":
            self.config = json.loads(open(f'{pathConfig}\\config.json', 'r', encoding="utf-8-sig").read())
            sys.exit()
        elif type == "maxdevice":
            self.uiFuncions.MessageBoxShow(text='Key hết hạn hoặc đạt giới hạn thiết bị!')
            sys.exit()
        elif type == 'notdevice':
            self.uiFuncions.MessageBoxShow(text='Không kiểm tra được key!!!')
            sys.exit()
        elif type == "connect":
            self.uiFuncions.MessageBoxShow(text='Không thể kết nối đến server!')
            sys.exit()
        else:
            self.setIconTitLe(type)
            self.setButtonStyle(type)
     
            if type == 'chrome':
                self.stackedWidget.setCurrentWidget(self.stack_chrome); self.load_data_chrome.start()
            elif type == 'tiktok':
                self.stackedWidget.setCurrentWidget(self.stack_tiktok)

    """Các chức năng của TableWidget"""
    def editRow(self, row, column, text, table):
        table.setItem(row, column, QtWidgets.QTableWidgetItem(text))
    
    def editCellByColumnName(self, row, columnName, text, table):
        if columnName in self.tableWidgetText:
            columnIndex = self.tableWidgetText.index(columnName)
            table.setItem(row, columnIndex, QtWidgets.QTableWidgetItem(text))

    def menuTikTok(self, event):
        menu = QtWidgets.QMenu()
        with open('src/themes/qmenu.css','r') as file:
            menu.setStyleSheet(file.read())
        
        start             = menu.addAction("Start")
        stop              = menu.addAction("Stop")
        chrome            = menu.addMenu('Chrome')
        getCookie         = chrome.addAction("Get Cookies")
        openChrome        = chrome.addAction("Open Chrome")
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\images\\chrome.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);chrome.setIcon(icon)
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\images\\login.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);getCookie.setIcon(icon)
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\images\\chrome.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);openChrome.setIcon(icon)
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\images\\play.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);start.setIcon(icon)
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\images\\stop-button.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);stop.setIcon(icon)
        
        openFile          = menu.addMenu('Open File')
        openAccTikTok     = openFile.addAction('Accounts')
        openAccTikTokUsed = openFile.addAction('Used')

        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\images\\open-book.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);openFile.setIcon(icon)
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\images\\tik-tok (1).png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);openAccTikTok.setIcon(icon)
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\images\\used.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);openAccTikTokUsed.setIcon(icon)
        
        openError         = openFile.addAction('Error')
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\images\\errorlog.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);openError.setIcon(icon)
        
        coppy             = menu.addMenu('Coppy')
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\\images\\copy.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);coppy.setIcon(icon)
        coppy1            = coppy.addAction('UID')
        coppy2            = coppy.addAction('Cookie')
        coppy3            = coppy.addAction('UID|Password|Cookie')
        coppy4            = coppy.addAction('UserCoin|PassCoin')
        
        pasteclipboard    = menu.addMenu('Paste Clipboard')
        pasteProxy        = pasteclipboard.addAction("Proxy")
        pasteCk           = pasteclipboard.addAction("Cookie")
        pasteID           = pasteclipboard.addAction("IDProfile")
        pasteAcc          = pasteclipboard.addAction("UID|Password")

        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\\images\\database.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);pasteclipboard.setIcon(icon)
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\images\\server.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);pasteProxy.setIcon(icon)
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\images\\cookie.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);pasteCk.setIcon(icon)
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\images\\id.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);pasteID.setIcon(icon)
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\images\\user.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);pasteAcc.setIcon(icon)
        
        
        delete            = menu.addMenu("Delete")
        deleteAcc         = delete.addAction("Delete Accounts")
        deleteProfile     = delete.addAction("Delete Profile")
        load              = menu.addAction("Reload")
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\\images\\managedel.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);delete.setIcon(icon)
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\\images\\deluser.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);deleteAcc.setIcon(icon)
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\\images\\chrome.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);deleteProfile.setIcon(icon)
        icon              = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\\images\\available_updates_25px.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);load.setIcon(icon)

        action            = menu.exec_(QtGui.QCursor.pos())

        if action == start:
            row = self.tableWidget.selectionModel().selectedRows()
            for r in range(len(row)):
                self.startMining(r=row[r].row(), totalThread=len(row))
                
        if action == stop:
            row = self.tableWidget.selectionModel().selectedRows()
            for r in range(len(row)):
                self.stopMining(r=row[r].row())

        if action == getCookie or action == openChrome:
            if action == getCookie: funcion = 'getCookie'
            else:funcion = 'openChrome'
            self.settings  = json.loads(open(f'{pathConfig}\\settings.json', 'r', encoding="utf-8-sig").read())
            from src.modules.autoChrome import StartChrome
            row = self.tableWidget.selectionModel().selectedRows()
            row_indexes = [row.row() for row in row]
            self.nextThread = iter(row_indexes)
            print(len(row_indexes))
            for r in range(self.settings['maxThreadChrome']):
                try:
                    row = next(self.nextThread)
                    self.startMining(r=row, type=funcion, totalThread=len(row_indexes)-1)
                except StopIteration:pass
        
        if action == openAccTikTok or action == openAccTikTokUsed or action == openError:
            
                fileName = ''
                if action.text()    ==  'Accounts' : fileName = 'tiktok.txt'; self.iterDataTikTok = []
                elif action.text()  ==  'Used'   : fileName = 'used.txt'
                file = os.path.join(pathDataTikTok, fileName)
                if action.text()  ==  'Error'  : file = errorlog
                if not os.path.exists(file):
                    try:
                        with open(file, 'w', encoding='utf-8') as f:
                            os.startfile(file)
                    except OSError:self.uiFuncions.MessageBoxShow(text=f'Có lỗi khi tạo tệp tin {fileName}!!!')
                else:
                    try:os.startfile(file)
                    except OSError:self.uiFuncions.MessageBoxShow(text=f'Có lỗi khi mở tệp tin {fileName}!!!')
        if action == pasteID:
            row = self.tableWidget.selectionModel().selectedRows()
            for r, id in zip(range(len(row)),pyperclip.paste().split('\n')):
                self.editCellByColumnName(row[r].row(),'Profile ID',id,self.tableWidget)
            self.saveDataTable()
        if action == pasteProxy:
            row = self.tableWidget.selectionModel().selectedRows()
            for r, ck in zip(range(len(row)),pyperclip.paste().split('\n')):
                self.editCellByColumnName(row[r].row(),'Proxy',ck,self.tableWidget)
            self.saveDataTable()
        
        if action == pasteCk:
            row = self.tableWidget.selectionModel().selectedRows()
            for r, ck in zip(range(len(row)),pyperclip.paste().split('\n')):
                self.editCellByColumnName(row[r].row() ,'UID', '',self.tableWidget)
                self.editCellByColumnName(row[r].row() ,'Password', '' ,self.tableWidget)
                self.editCellByColumnName(row[r].row(),'Cookie',ck,self.tableWidget)
            self.saveDataTable()
            
        if action == pasteAcc:
            try:
                row = self.tableWidget.selectionModel().selectedRows()
                for r, acc in zip(range(len(row)),pyperclip.paste().split('\n')):
                    if '|' in acc:
                        self.editCellByColumnName(row[r].row() ,'UID', acc.split('|')[0] ,self.tableWidget)
                        self.editCellByColumnName(row[r].row() ,'Password', acc.split('|')[1] ,self.tableWidget)
                        self.editCellByColumnName(row[r].row() ,'Cookie', '' ,self.tableWidget)
                    else:self.uiFuncions.MessageBoxShow(text="Kiểm tra lại định dạng!!!");return
                self.saveDataTable()
            except:self.uiFuncions.MessageBoxShow(text="Kiểm tra lại định dạng!!!")
            
        if action == coppy3 or action == coppy1 or action == coppy2 or action == coppy4:
                columnName = action.text()
            
                if action == coppy3:
                    row = self.tableWidget.selectionModel().selectedRows()
                    copied_data = '\n'.join(['|'.join([self.tableWidget.item(r.row(), i).text() for i in range(1, 4)]) for r in row if all(self.tableWidget.item(r.row(), i) for i in range(1, 4))])
                    QApplication.clipboard().setText(copied_data)
                if action == coppy4:
                    row = self.tableWidget.selectionModel().selectedRows()
                    copied_data = '\n'.join(['|'.join([self.tableWidget.item(r.row(), i).text() for i in range(5, 7)]) for r in row if all(self.tableWidget.item(r.row(), i) for i in range(5, 7))])
                    QApplication.clipboard().setText(copied_data)
                else:
                    selected_column_name = action.text()
                    column_index = next((i for i in range(self.tableWidget.columnCount()) if self.tableWidget.horizontalHeaderItem(i).text() == selected_column_name), -1)
                    if column_index != -1:
                        copied_data = '\n'.join(self.tableWidget.item(r.row(), column_index).text() for r in self.tableWidget.selectionModel().selectedRows())
                        QApplication.clipboard().setText(copied_data)

        if action == deleteAcc:
        
            if self.threadOn == {}:
                self.deleteDataTable()
            else:self.uiFuncions.MessageBoxShow(text="Không nhập hoặc xóa dữ liệu trong quá trình chạy để tránh xảy ra lỗi!!!")
        
        if action == deleteProfile:
            row = self.tableWidget.selectionModel().selectedRows()
            try:
                for r in range(len(row)):
                    nameFolder = self.tableWidget.item(row[r].row(), 5).text()
                    if os.path.exists(pathChrome+'\\'+nameFolder) and os.path.isdir(pathChrome+'\\'+nameFolder):
                        try:
                            thread = threading.Thread(target=self.remove_folder, args=(pathChrome+'\\'+nameFolder,)).start()
                            self.editCellByColumnName(row[r].row(), 'Trạng Thái', f'File deletion [{nameFolder}] was successful.' ,self.tableWidget)
                        except:self.editCellByColumnName(row[r].row(), 'Trạng Thái', f'Deleting profile [{nameFolder}] failed!' ,self.tableWidget)
            except:self.uiFuncions.MessageBoxShow(text="Bạn cần chạy tools với quyền admin để sử dụng chức năng này!!!")
        if action == load:
            if self.threadOn == {}:
                try:
                    file = os.path.join(pathDataTikTok, 'tiktok.txt')
                    with open(file,'r' ,encoding='utf-8') as f:
                        file = [line.strip() for line in f if line.strip()]
                        self.iterDataTikTok = iter(file)
                except Exception as e:self.iterDataTikTok = []
                self.load_data.start()
            else:self.uiFuncions.MessageBoxShow(text="Không tải lại dữ liệu trong quá trình chạy để tránh xảy ra lỗi!!!")
    
    def remove_folder(self, path):
        shutil.rmtree(path)
    
    def saveDataTable(self):
        try:
            try:
                with open(f"{pathConfig}/config.json", "r", encoding='utf-8') as file:
                    dataJson = json.load(file)
            except:
                dataJson = {}
            dataJson['inputFormat'] = [
        "STT", "UID", "Password", "Cookie", "Name", "UserCoin", "PassCoin", "XuHT", "XuThem","Tỷ lệ",
        "Job/Cache","Profile ID" ,"Proxy", "Trạng Thái", "Action"
        ]
            writeJson(f"{pathConfig}\\config.json", dataJson)
            with open(f'{pathData}\\{self.folderCbox.currentText()}.txt', 'w', encoding='utf-8') as file:
                for row in range(self.tableWidget.rowCount()):
                    row_data = []
                    for column in range(self.tableWidget.columnCount()):
                        item = self.tableWidget.item(row, column)
                        if item is not None:
                            row_data.append(item.text().strip())
                        else:
                            row_data.append('')
                    row_line = '|'.join(row_data) + '\n'
                    file.write(row_line)
        except Exception as e:
            print(e,'error savedatatable')
    
    def deleteDataTable(self):
        try:
            selectRe = []
            row = self.tableWidget.selectionModel().selectedRows()
            with open(f'{pathData}\\{self.folderCbox.currentText()}.txt', 'r', encoding="utf-8") as file:
                lines = file.readlines()
            # Tạo danh sách các chỉ số cần loại bỏ từ dữ liệu
            selectRe = [r.row() for r in row]
            # Loại bỏ các dòng được chọn từ danh sách dữ liệu
            filtered_lines = [line for index, line in enumerate(lines) if index not in selectRe]
            with open(f'{pathData}\\{self.folderCbox.currentText()}.txt', 'w', encoding='utf-8') as file:
                file.writelines(filtered_lines)

            self.load_data.start()

        except:pass
    
    def menuChrome(self, event):
        self.config = json.loads(open(f'{pathConfig}\\config.json', 'r', encoding="utf-8-sig").read())
    
        menu = QtWidgets.QMenu()
        with open('src/themes/qmenu.css','r') as file:
            menu.setStyleSheet(file.read())
        
        delete = menu.addAction("Delete Profile")
        icon = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\\images\\trash-can.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);delete.setIcon(icon)
        load = menu.addAction("Reload")
        icon = QtGui.QIcon();icon.addPixmap(QtGui.QPixmap("images\\images\\available_updates_25px.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off);load.setIcon(icon)
        action = menu.exec_(QtGui.QCursor.pos())

        if action == delete:
            row = self.tableWidgetChrome.selectionModel().selectedRows()
            for r in range(len(row)):
                nameFolder = self.tableWidgetChrome.item(row[r].row(), 1).text()
                if os.path.exists(pathChrome+'\\'+nameFolder) and os.path.isdir(pathChrome+'\\'+nameFolder):
                    try:
                        shutil.rmtree(pathChrome + '\\' + nameFolder)
                        self.editRow(row[r].row(), 3, f'File deletion [{nameFolder}] was successful.' ,self.tableWidgetChrome)
                    except:self.editRow(row[r].row(), 3, f'Deleting profile [{nameFolder}] failed!' ,self.tableWidgetChrome)

        if action == load:
            self.load_data_chrome.start()
         

    def tableSelected(self):
        selected_items = self.tableWidget.selectedItems()
        selected_rows = set()
        for item in selected_items:
            selected_rows.add(item.row())

        num_selected_rows = len(selected_rows)
        self.Blacken = num_selected_rows
        self.statusBar()

    """Các chức năng của Folder"""
    def loadFolder(self):
        try:
            with open(f"{pathConfig}/config.json", "r") as file:
                dataJson = json.load(file)
        except:
            dataJson = {}
        dataJson['group'] = {'count':len(os.listdir(pathData))}
        with open(f"{pathConfig}\\config.json", "w+", encoding='utf-8') as outfile:
            json.dump(dataJson, outfile, indent=2)
        if dataJson['group']['count'] == 0:
            self.folderCbox.addItem("")
        for i in range(dataJson['group']['count']-1):
            self.folderCbox.addItem("")
        for t in range(2):
            file_list = [file_name for file_name in os.listdir(pathData) if file_name.endswith('.txt')]
            if '[ All Accounts ].txt' not in file_list or file_list == []:
                self.folderCbox.setItemText(0, "[ All Accounts ]")
                if os.path.exists(f'{pathData}\\[ All Accounts ].txt') == False:
                    with open(f"{pathData}\\[ All Accounts ].txt", "w", encoding='utf-8'):pass
            else:
                file_list = sorted(file_list, key=lambda x: (x != '[ All Accounts ].txt', os.path.getctime(os.path.join(pathData, x))))
                for t, file_name in zip(range(dataJson['group']['count']+1),file_list):
                    if file_name.endswith('.txt'):
                        self.folderCbox.setItemText(t, str(file_name.split('.txt')[0]))
                break

    def loadDataFile(self):
        try:
            self.dataAccounts = open(f"{pathData}\\{self.folderCbox.currentText()}.txt", "r", encoding="utf-8").read().strip().split('\n')
            if '[ All Accounts ]' not in self.folderCbox.currentText():
                self.load_data.start()
            else:
                emptyFile(f'{pathData}\\[ All Accounts ].txt') # xóa dữ liệu trong file
                txt_files = [f for f in os.listdir(pathData) if f.endswith('.txt')]
                # Ghi nội dung của từng tệp tin .txt vào tệp tin đầu ra
                for file_name in txt_files:
                    file_path = os.path.join(pathData, file_name)
                    if "[ All Accounts ]" not in file_path:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            text = f.read()
                            writeFile(f'{pathData}\\[ All Accounts ].txt',text+'\n')
                self.load_data.start()
                
        except:pass
        
    def addNewFolder(self):
        nameFolder, ok = self.uiFuncions.InputDiaLogGetText(title='Input File Name',text1 = 'File name:', text2 = 'PyTournes ( Group )')
        if ok:
            try:
                with open(f"{pathConfig}/config.json", "r") as file:
                    dataJson = json.load(file)
            except:
                dataJson = {}
            if os.path.exists(pathData+'\\'+nameFolder+'.txt'):self.uiFuncions.MessageBoxShow(text=f'File "{nameFolder}" already exist.');return
            
            self.folderCbox.addItem(nameFolder)
            dataJson['group'] = {'count':self.folderCbox.count()+1}
            writeJson(f"{pathConfig}\\config.json",dataJson)
            writeFile(f'{pathData}\\{nameFolder}.txt','')
            self.folderCbox.setCurrentIndex(self.folderCbox.count()-1)
    
    def editFolder(self):
        if self.folderCbox.currentText() != '[ All Accounts ]':
            nameFolder, ok = self.uiFuncions.InputDiaLogGetText(title='Enter the file name to edit',text1 = 'File name:', text2 = self.folderCbox.currentText())
            if ok:
                if os.path.exists(pathData+'\\'+nameFolder+'.txt'):self.uiFuncions.MessageBoxShow(text=f'File "{nameFolder}" already exist.');return
                os.rename(f"{pathData}\\{self.folderCbox.currentText()}.txt",f"{pathData}\\{nameFolder}.txt")
                self.folderCbox.setItemText(self.folderCbox.currentIndex(), nameFolder) 
        else:
            self.uiFuncions.MessageBoxShow(text=f'File names cannot be edited {self.folderCbox.currentText()}')         
    
    def deleteFolder(self):
        try:
            with open(f"{pathConfig}/config.json", "r") as file:
                dataJson = json.load(file)
        except:
            dataJson = {}
        if '[ All Accounts ]' in self.folderCbox.currentText():
            self.uiFuncions.MessageBoxShow(text=f'File deletion is not allowed "{self.folderCbox.currentText()}" !')
            return
        if self.uiFuncions.MessageBoxShow(text=f'Are you sure you want to delete the file "{self.folderCbox.currentText()}"'):
            if os.path.exists(f"{pathData}\\{self.folderCbox.currentText()}.txt",):
                os.remove(f"{pathData}\\{self.folderCbox.currentText()}.txt")
            index = self.folderCbox.findText(self.folderCbox.currentText())
            self.folderCbox.removeItem(index)
            count = len(self.folderCbox)
        
            dataJson['group'] = {'count':count}
            writeJson(f"{pathConfig}\\config.json",dataJson)

    """Chức năng của Kiếm Xu"""
    def startMining(self, r, type = '', totalThread = 0):
        while True:
            self.settings  = json.loads(open(f'{pathConfig}\\settings.json', 'r', encoding="utf-8-sig").read())
        
            if len(self.MiningOn)+1 <= self.settings['maxThreadChrome']:
                if r not in self.MiningOn:
                    self.threadOn[r] = StartChrome(self, r, type, totalThread)
                    self.threadOn[r].setItem.connect(self.editRow)
                    self.threadOn[r].EnableStart.connect(self.actionTable.EnableStart)
                    self.threadOn[r].statusBar.connect(self.statusBar)
                    self.threadOn[r].startMining.connect(self.startMining)
                    self.threadOn[r].stopMining.connect(self.stopMining)
                    self.threadOn[r].editCellByColumnName.connect(self.editCellByColumnName)
                    self.threadOn[r].start()
                    self.MiningOn.append(r)
                    self.actionTable.EnableStop(r, self.tableWidget, 14)
                    return 
                else:self.uiFuncions.MessageBoxShow(text=f"Luồng [ {r+1} ] đang được khởi động!!!");return
            self.editCellByColumnName(r, 'Trạng Thái', 'Chờ đến lượt khởi động!!!', self.tableWidget)
            delayQThread(3)
 
    def stopMining(self, r):
        try:
            self.MiningOn.remove(r)
            self.threadOn[r].stop()
            del self.threadOn[r]
            self.actionTable.EnableStart(r , self.tableWidget, 14)
        except:pass

    """Chức năng của thanh QLabel Status"""
    def convertVND(self, amount):
        formatted_a = '{:,.0f}'.format(amount).replace(',', '.')
        return formatted_a
    
    def statusBar(self, type = '', data = 0):
        if type == 'jobs':self.Jobs +=1
        if type == 'coinR':self.CoinR += data
        self.AllTable = self.tableWidget.rowCount()

        self.lblStatus.setText(f'<html><head/><body><p><span style=" color:#5f8899;">Blacken: </span><span style=" font-weight:600; color:#5fbc5a;">{self.Blacken} </span><span style=" color:#5f8899;">| </span><span style=" color:#5f8899;">All: </span><span style=" font-weight:600; color:#5fbc5a;">{self.AllTable}</span></span><span style=" color:#5f8899;"> | Jobs: </span><span style=" font-weight:600; color:#5fbc5a;">{self.convertVND(self.Jobs)} </span><span style=" color:#5f8899;"> | Coin: </span><span style=" font-weight:600; color:#5fbc5a;">{self.convertVND(self.Coin)} </span><span style=" color:#5f8899;">| Coin received: </span><span style=" font-weight:600; color:#5fbc5a;">{self.convertVND(self.CoinR)} </span><span style=" color:#5f8899;">| Device Online: </span><span style=" font-weight:600; color:#5fbc5a;">{self.DOnl}</span><span style=" color:#5f8899;">/</span><span style=" font-weight:600; color:#5fbc5a;">{self.MaxDevice} </span><span style=" color:#5f8899;">| Device ID: </span><span style=" font-weight:600; color:#5fbc5a;">{self.DId} </span><span style=" color:#5f8899;">| Your IP: </span><span style=" font-weight:600; color:#5fbc5a;">{self.Ip}</span></p></body></html>')

        self.lblTimeoutSV.setText(f'<html><head/><body><p><span style=" color:#5f8899;">Processed in </span><span style=" font-weight:600; color:#5fbc5a;">{self.timeoutSV} </span><span style=" color:#5f8899;">second(s)</span></p></body></html>')

    """"Các chức năng khác phía bên góc phải"""
    def toggle_maximized(self):
        # Nếu HorizontalLayout của khung MainPages không được đặt về 0 thì sẽ dư thừa khi zoom
        if not self.APP.isMaximized():self.APP.showMaximized();self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        else:self.APP.showNormal();self.horizontalLayout.setContentsMargins(6, 6, 6, 6)
        
    def closeAPP(self):  
        if self.uiFuncions.MessageBoxShow(text='Bạn có muốn thoát khỏi chương trình?'):sys.exit()


