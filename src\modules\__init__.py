# ///////////////////////////////////////////////////////////////
#
# BY: Tournes or DANG DINH TOAN
# PROJECT MADE WITH: Qt Designer and PyQt5
# 
# ///////////////////////////////////////////////////////////////

# PASSWORD DELEGATE FUNCION
from . passwordDelegate import PasswordDelegate


# FUNCION TO UPLOAD DATA TO THE TABLE
from . loaddatatable import *


# QTABLEWIDGET COMBOBOX FEATURES
from .actionTable import ActionTable

# SET ICON
from .seticon import setIcon

# MESSAGEBOX INFO
from .uifuncions import UiFuncions

# BYBASSCAPTCHA TIKTOK
from .bypassCatpcha import *

# AUTOCHROME TIKTOK
from .autoChrome import StartChrome

