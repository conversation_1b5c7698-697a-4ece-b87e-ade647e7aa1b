{"background": {"persistent": false, "scripts": ["lib/config.js", "lib/chrome.js", "lib/runtime.js", "lib/common.js"]}, "browser_action": {"default_icon": {"16": "data/icons/16.png", "32": "data/icons/32.png", "48": "data/icons/48.png", "64": "data/icons/64.png"}, "default_popup": "data/popup/popup.html", "default_title": "WebGL Fingerprint Defender"}, "content_scripts": [{"all_frames": true, "js": ["data/content_script/inject.js"], "match_about_blank": true, "matches": ["*://*/*"], "run_at": "document_start"}], "description": "Defending against WebGL fingerprinting by reporting a fake value.", "homepage_url": "https://mybrowseraddon.com/webgl-defender.html", "icons": {"128": "data/icons/128.png", "16": "data/icons/16.png", "32": "data/icons/32.png", "48": "data/icons/48.png", "64": "data/icons/64.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhTPEFqJ3hWo6ssWPtSmuXIQuKWHDa6gBwyL23deYl5fmG2fFk41xfGEWGaAWZ1+Uwz5tU/Y4FovZfWcvDzQe109kwPWwkHF/zEotHmPj/+wapOMFluDQ+qExLImU3ROxDIzHxGDIfEnOvyVKbNH9ZWwgJytwXDOnDZ8OepXNl/2cLKeS67PN4/7w811C2pA1FlHYY9rhPBDWX9OkbMTSDTanPoJTcGW3sgkZFY6Ao8zJSj3DrxUEhv/b0cwlgFSgf347zci7VgP1I1z/Lt31pw/+gTaa4FKwCrSR5AAsZZ+ZpKKXeygoH+AsTYypXYiIY9VVtLvpjevdDG2tgaq1xwIDAQAB", "manifest_version": 2, "name": "WebGL Fingerprint Defender", "offline_enabled": true, "permissions": ["storage", "contextMenus", "notifications"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "0.1.6"}