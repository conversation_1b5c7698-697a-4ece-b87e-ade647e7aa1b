import requests


def addProxy(token, idprofile, proxy):
    if len(proxy.split(':')) >= 4:
        ip, port, username, password = proxy.split(':')[0], proxy.split(':')[1], proxy.split(':')[2], proxy.split(':')[3]
        
    elif len(proxy.split(':')) >= 2:
        ip, port, username, password = proxy.split(':')[0], proxy.split(':')[1], '',''
    else:
        ip, port, username, password = '','','',''
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json',
    }

    json_data = {
        'autoProxyRegion': 'us',
        'host': ip,
        'mode': 'http',
        'password': password,
        'port': port,
        'torProxyRegion': 'us',
        'username': username,
    }

    response = requests.patch(f'https://api.gologin.com/browser/{idprofile}/proxy', headers=headers, json=json_data)