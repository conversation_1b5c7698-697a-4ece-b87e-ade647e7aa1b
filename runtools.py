from Core import *
from src.modules import *

class Ui_SignIn(object):
    gripSize = 16; grips = []; _old_pos = None
    def setupUi(self, SignIn):
        SignIn.setObjectName("SignIn")
        SignIn.setFixedSize(394, 527)
        SignIn.setWindowIcon(QtGui.QIcon('icon.ico'))
        SignIn.setAttribute(Qt.WA_TranslucentBackground)
        SignIn.setWindowFlags(Qt.FramelessWindowHint)
        self.stylesheet = QtWidgets.QWidget(SignIn)
        self.stylesheet.setStyleSheet("* {\n"
"    background-color: transparent;\n"
"    background: none;\n"
"    border: none;\n"
"    padding: 0;\n"
"    margin: 0;\n"
"    font: 10pt \"Segoe UI\";\n"
"    background: no-repeat;\n"
"    color: #fff;\n"
"}\n"
"\n"
"QWidget {\n"
"    color: rgb(221, 221, 221);\n"
"    font: 10pt \"Segoe UI\";\n"
"}\n"
"\n"
"#MainPages {\n"
"    border-radius: 5px;\n"
"    background-color: #0e0e0e;\n"
"}\n"
"\n"
"\n"
"\n"
"")
        self.stylesheet.setObjectName("stylesheet")
        self.gridLayout_6 = QtWidgets.QGridLayout(self.stylesheet)
        self.gridLayout_6.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_6.setSpacing(0)
        self.gridLayout_6.setObjectName("gridLayout_6")
        self.MainPages = QtWidgets.QFrame(self.stylesheet)
        self.MainPages.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.MainPages.setFrameShadow(QtWidgets.QFrame.Raised)
        self.MainPages.setObjectName("MainPages")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.MainPages)
        self.horizontalLayout.setContentsMargins(6, 6, 6, 6)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.frame = QtWidgets.QFrame(self.MainPages)
        self.frame.setStyleSheet("background-color:#171717;")
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.Title = QtWidgets.QFrame(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.Title.sizePolicy().hasHeightForWidth())
        self.Title.setSizePolicy(sizePolicy)
        self.Title.setStyleSheet("*{\n"
"padding:3 0;\n"
"background-color:#171717;\n"
"\n"
"}\n"
"\n"
"QFrame #Title{\n"
"\n"
"border-bottom: 2px solid #3c4454;\n"
"}\n"
"\n"
"")
        self.Title.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.Title.setFrameShadow(QtWidgets.QFrame.Raised)
        self.Title.setObjectName("Title")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.Title)
        self.horizontalLayout_3.setContentsMargins(3, 0, 0, 0)
        self.horizontalLayout_3.setSpacing(0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.leftTitle = QtWidgets.QFrame(self.Title)
        self.leftTitle.setStyleSheet("")
        self.leftTitle.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.leftTitle.setFrameShadow(QtWidgets.QFrame.Raised)
        self.leftTitle.setObjectName("leftTitle")
        self.gridLayout_3 = QtWidgets.QGridLayout(self.leftTitle)
        self.gridLayout_3.setContentsMargins(5, 0, 0, 0)
        self.gridLayout_3.setSpacing(0)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.btn_title = QtWidgets.QPushButton(self.leftTitle)
        self.btn_title.setStyleSheet("\n"
"font:bold;\n"
"")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(".\\images/images/login.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_title.setIcon(icon)
        self.btn_title.setIconSize(QtCore.QSize(24, 24))
        self.btn_title.setObjectName("btn_title")
        self.gridLayout_3.addWidget(self.btn_title, 0, 0, 1, 1, QtCore.Qt.AlignLeft)
        self.horizontalLayout_3.addWidget(self.leftTitle)
        self.rightButtons = QtWidgets.QFrame(self.Title)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.rightButtons.sizePolicy().hasHeightForWidth())
        self.rightButtons.setSizePolicy(sizePolicy)
        self.rightButtons.setStyleSheet("\n"
"QPushButton:hover {\n"
"    background-color: #3c4454;\n"
"    border-radius:5px;\n"
"\n"
"\n"
"}\n"
"QPushButton:pressed { \n"
"    background-color: #2c313c;\n"
"     border-radius:5px;\n"
"}\n"
"\n"
"\n"
"\n"
"")
        self.rightButtons.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.rightButtons.setFrameShadow(QtWidgets.QFrame.Raised)
        self.rightButtons.setObjectName("rightButtons")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.rightButtons)
        self.horizontalLayout_2.setContentsMargins(0, 0, 6, 0)
        self.horizontalLayout_2.setSpacing(3)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.minimizeAppBtn = QtWidgets.QPushButton(self.rightButtons)
        self.minimizeAppBtn.setEnabled(False)
        self.minimizeAppBtn.setStyleSheet("")
        self.minimizeAppBtn.setText("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(".\\images/icons/icon_minimize.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.minimizeAppBtn.setIcon(icon1)
        self.minimizeAppBtn.setIconSize(QtCore.QSize(28, 24))
        self.minimizeAppBtn.setObjectName("minimizeAppBtn")
        self.horizontalLayout_2.addWidget(self.minimizeAppBtn)
        self.maximizeRestoreAppBtn = QtWidgets.QPushButton(self.rightButtons)
        self.maximizeRestoreAppBtn.setEnabled(False)
        self.maximizeRestoreAppBtn.setStyleSheet("")
        self.maximizeRestoreAppBtn.setText("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(".\\images/icons/icon_maximize.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.maximizeRestoreAppBtn.setIcon(icon2)
        self.maximizeRestoreAppBtn.setIconSize(QtCore.QSize(28, 24))
        self.maximizeRestoreAppBtn.setObjectName("maximizeRestoreAppBtn")
        self.horizontalLayout_2.addWidget(self.maximizeRestoreAppBtn)
        self.closeAppBtn = QtWidgets.QPushButton(self.rightButtons)
        self.closeAppBtn.setStyleSheet("\n"
"QPushButton:pressed { \n"
"background-color: #ff5555;\n"
" border-style: solid; border-radius: 4px; \n"
"}\n"
"")
        self.closeAppBtn.setText("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(".\\images/icons/icon_close.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.closeAppBtn.setIcon(icon3)
        self.closeAppBtn.setIconSize(QtCore.QSize(28, 24))
        self.closeAppBtn.setObjectName("closeAppBtn")
        self.horizontalLayout_2.addWidget(self.closeAppBtn)
        self.horizontalLayout_3.addWidget(self.rightButtons)
        self.verticalLayout.addWidget(self.Title)
        self.bodyPages = QtWidgets.QFrame(self.frame)
        self.bodyPages.setStyleSheet("")
        self.bodyPages.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.bodyPages.setFrameShadow(QtWidgets.QFrame.Raised)
        self.bodyPages.setObjectName("bodyPages")
        self.gridLayout_4 = QtWidgets.QGridLayout(self.bodyPages)
        self.gridLayout_4.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_4.setSpacing(0)
        self.gridLayout_4.setObjectName("gridLayout_4")
        self.frameBody = QtWidgets.QFrame(self.bodyPages)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frameBody.sizePolicy().hasHeightForWidth())
        self.frameBody.setSizePolicy(sizePolicy)
        self.frameBody.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.frameBody.setStyleSheet("*{\n"
"    background-color: #171717;\n"
"}\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"LineEdit */\n"
"QLineEdit {\n"
"    background-color:#343434;\n"
"    border-radius: 5px;\n"
"    border: 2px solid #343434;\n"
"    padding-left: 10px;\n"
"    height:40px;\n"
"    selection-color: rgb(255, 255, 255);\n"
"    selection-background-color: rgb(255, 121, 198);\n"
"}\n"
"\n"
"QLineEdit:hover {\n"
"    border: 2px solid rgb(64, 71, 88);\n"
"}\n"
"\n"
"QLineEdit:focus {\n"
"    border: 2px solid rgb(91, 101, 124);\n"
"}")
        self.frameBody.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frameBody.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frameBody.setObjectName("frameBody")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frameBody)
        self.verticalLayout_2.setContentsMargins(-1, -1, -1, 9)
        self.verticalLayout_2.setSpacing(10)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.titleWell = QtWidgets.QLabel(self.frameBody)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.titleWell.sizePolicy().hasHeightForWidth())
        self.titleWell.setSizePolicy(sizePolicy)
        self.titleWell.setStyleSheet("font:bold;\n"
"font: 20pt \"MesloLGMDZ NFM\";\n"
"padding-bottom:30px;")
        self.titleWell.setObjectName("titleWell")
        self.verticalLayout_2.addWidget(self.titleWell, 0, QtCore.Qt.AlignHCenter)
        self.username = QtWidgets.QLineEdit(self.frameBody)
        self.username.setStyleSheet("")
        self.username.setClearButtonEnabled(False)
        self.username.setObjectName("username")
        self.verticalLayout_2.addWidget(self.username)
        self.password = QtWidgets.QLineEdit(self.frameBody)
        self.password.setStyleSheet("")
        self.password.setObjectName("password")
        self.verticalLayout_2.addWidget(self.password)
        self.forgot = QtWidgets.QLabel(self.frameBody)
        self.forgot.setStyleSheet("color:#5f8899;\n"
"\n"
"padding-bottom:20px;")
        self.forgot.setObjectName("forgot")
        self.verticalLayout_2.addWidget(self.forgot)
        self.btnLogin = QtWidgets.QPushButton(self.frameBody)
        font = QtGui.QFont()
        font.setFamily("MS Shell Dlg 2")
        font.setPointSize(11)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.btnLogin.setFont(font)
        self.btnLogin.setStyleSheet("* {\n"
"    background-color: #0070f3;\n"
"    border-radius:3px;\n"
"\n"
"    font: 75 11pt \"MS Shell Dlg 2\";\n"
"    font: bold;\n"
"}\n"
"\n"
"QPushButton {\n"
"    height: 45px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #1883ff;\n"
"\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color:  #4759e6;\n"
"\n"
"}")
        self.btnLogin.setObjectName("btnLogin")
        self.verticalLayout_2.addWidget(self.btnLogin)
        self.create = QtWidgets.QLabel(self.frameBody)
        self.create.setStyleSheet("padding-top:30px;")
        self.create.setObjectName("create")
        self.verticalLayout_2.addWidget(self.create, 0, QtCore.Qt.AlignHCenter)
        self.gridLayout_4.addWidget(self.frameBody, 1, 0, 1, 1, QtCore.Qt.AlignVCenter)
        self.verticalLayout.addWidget(self.bodyPages)
        self.bottom = QtWidgets.QFrame(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.bottom.sizePolicy().hasHeightForWidth())
        self.bottom.setSizePolicy(sizePolicy)
        self.bottom.setStyleSheet("background-color: #181818;\n"
"\n"
"color: rgb(138, 149, 170);\n"
"height:30px;\n"
"")
        self.bottom.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.bottom.setFrameShadow(QtWidgets.QFrame.Raised)
        self.bottom.setObjectName("bottom")
        self.horizontalLayout_32 = QtWidgets.QHBoxLayout(self.bottom)
        self.horizontalLayout_32.setContentsMargins(5, 5, 5, 5)
        self.horizontalLayout_32.setSpacing(0)
        self.horizontalLayout_32.setObjectName("horizontalLayout_32")
        self.label = QtWidgets.QLabel(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setObjectName("label")
        self.horizontalLayout_32.addWidget(self.label)
        self.lblPyTournes1 = QtWidgets.QLabel(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lblPyTournes1.sizePolicy().hasHeightForWidth())
        self.lblPyTournes1.setSizePolicy(sizePolicy)
        self.lblPyTournes1.setStyleSheet("QLabel {\n"
"color:#5fbc5a;\n"
"font:bold;\n"
"}\n"
"QLabel:hover{\n"
"    color:#ff0000;\n"
"}\n"
"")
        self.lblPyTournes1.setObjectName("lblPyTournes1")
        self.horizontalLayout_32.addWidget(self.lblPyTournes1)
        self.label_3 = QtWidgets.QLabel(self.bottom)
        self.label_3.setObjectName("label_3")
        self.horizontalLayout_32.addWidget(self.label_3)
        self.label_41 = QtWidgets.QLabel(self.bottom)
        self.label_41.setText("")
        self.label_41.setObjectName("label_41")
        self.horizontalLayout_32.addWidget(self.label_41)
        self.verticalLayout.addWidget(self.bottom)
        self.horizontalLayout.addWidget(self.frame)
        self.gridLayout_6.addWidget(self.MainPages, 0, 0, 1, 1)
        SignIn.setCentralWidget(self.stylesheet)
        self.password.setEchoMode(QLineEdit.Password)
        self.retranslateUi(SignIn)
        QtCore.QMetaObject.connectSlotsByName(SignIn)

    def retranslateUi(self, SignIn):
        _translate = QtCore.QCoreApplication.translate
        SignIn.setWindowTitle(_translate("SignIn", "PyTournes - LOGIN"))
        self.btn_title.setText(_translate("SignIn", "Login Page"))
        self.titleWell.setText(_translate("SignIn", "Well comback"))
        self.username.setPlaceholderText(_translate("SignIn", "Mobile number, email or username"))
        self.password.setPlaceholderText(_translate("SignIn", "Password"))
        self.forgot.setText(_translate("SignIn", "Forgot your User Name or Password?"))
        self.btnLogin.setText(_translate("SignIn", "S i n g  I n"))
        self.create.setText(_translate("SignIn", "<html><head/><body><p>Create new account? <span style=\" color:#efd6ff;\">Sign up</span></p></body></html>"))
        self.label.setText(_translate("SignIn", "Developed by "))
        self.lblPyTournes1.setText(F"{SHELLVERSION}")
        self.label_3.setText(_translate("SignIn", "- Coppyright © 2023"))
        try:
            self.config = json.loads(open(f'{pathConfig}\\config.json', 'r', encoding="utf-8-sig").read())
            self.username.setText(self.config['account']['username'])
            self.password.setText(self.config['account']['password'])
        except:pass
        

        self.APP = SignIn
        
        # Tạo ra 4 grip để resize
        for i in range(4):grip = QSizeGrip(self.stylesheet);grip.resize(self.gripSize, self.gripSize);self.grips.append(grip)
        self.APP.moveEvent = self.APP
        self.APP.mousePressEvent = self.mousePressEvent
        self.APP.mouseMoveEvent = self.mouseMoveEvent

        self.btnLogin.clicked.connect(self.checkSignIn)
        self.closeAppBtn.clicked.connect(self.closeAPP)
        self.uiFuncions = UiFuncions(self)

        self.create.mousePressEvent = lambda event: self.openURL("create")
        self.forgot.mousePressEvent = lambda event: self.openURL("forgot")

        self.lblPyTournes1.mousePressEvent = lambda event: self.openURL('openurl')
        
    def openURL(self, label):
        QDesktopServices.openUrl(QUrl("https://zalo.me/**********"))
        if label == 'create':self.uiFuncions.MessageBoxShow(text='Liên hệ support để được hỗ trợ tạo tài khoản!!!')
        if label == 'forgot':self.uiFuncions.MessageBoxShow(text='Liên hệ support để được hỗ trợ lấy lại mật khẩu!!!')

    def checkSignIn(self):
      
        from src.pages.ui_app import Ui_APP
        
        self.APP.close()
        self.main = QtWidgets.QMainWindow()
        self.ui = Ui_APP()
        self.ui.setupUi(self.main)
        self.main.show()
        
            

    """Chức năng di chuyển zoom in zoom out APP"""
    def updateGripPositions(self):
        self.grips[0].move(0, 0) 
        self.grips[1].move(self.APP.width() - self.gripSize, 0) 
        self.grips[2].move(0, self.APP.height() - self.gripSize)  
        self.grips[3].move(self.APP.width() - self.gripSize, self.APP.height() - self.gripSize)

    def resizeEvent(self, event):
        self.APP.resizeEvent(event)
        self.updateGripPositions()

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self._old_pos = event.pos()
            
    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            self._old_pos = None
            
    def mouseMoveEvent(self, event):
        if not self._old_pos:
            return
        delta = event.pos() - self._old_pos
        self.APP.move(self.APP.pos() + delta)

    def closeAPP(self):  
        self.APP.close()

if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    SignIn = QtWidgets.QMainWindow()
    ui = Ui_SignIn()
    ui.setupUi(SignIn)
    SignIn.show()
    sys.exit(app.exec_())
