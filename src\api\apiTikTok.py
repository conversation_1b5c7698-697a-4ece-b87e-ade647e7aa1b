from library import *

class TikTok_Api(object):
    def __init__(self, cookie: str) -> None:
        self.cookie = cookie
        
    def infoAccounts(self, username = '') -> bool:
        try:
            headers = {
                'authority': 'www.tiktok.com',
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language': 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5',
                'cache-control': 'max-age=0',
                'cookie': self.cookie,
                'sec-ch-ua': '"Not/A)Brand";v="99", "Google Chrome";v="115", "Chromium";v="115"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'none',
                'sec-fetch-user': '?1',
                'upgrade-insecure-requests': '1',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
            }
            if username == '':
                response = requests.get('https://www.tiktok.com/', headers=headers,timeout = 5).text
                
                uid = response.split('"uid":"')[1].split('","nickName":"')[0]
                nick_name = response.split('"nickName":"')[1].split('","signature":""')[0]
                uniqueId = response.split('"uniqueId":"')[1].split('","')[0]
                response = requests.get('https://www.tiktok.com/@{}'.format(uniqueId), headers=headers).text
                # with open('data.txt','w',encoding='utf-8') as f:
                #     f.write(response)
                followerCount = response.split('"followerCount":')[1].split(',"')[0]
                followingCount = response.split('"followingCount":')[1].split(',"')[0]
                heartCount = response.split('"heartCount":')[1].split(',"')[0]
                videoCount = response.split('"videoCount":')[1].split(',"')[0]
                return {'live':True,'uid': uid, 'nickName': nick_name, 'uniqueId': uniqueId, 'followerCount':followerCount,'followingCount':followingCount,'heartCount':heartCount,'videoCount':videoCount} 
            else:
                response = requests.get('https://www.tiktok.com/@{}'.format(username), headers=headers,timeout = 5).text
                uid = response.split('{"id":"')[1].split('","shortId":"')[0]
                nick_name = response.split('","nickname":"')[1].split('","avatarLarger"')[0]
                uniqueId = response.split('","shortId":"","uniqueId":"')[1].split('","nickname"')[0]
                followerCount = response.split('"followerCount":')[1].split(',"')[0]
                followingCount = response.split('"followingCount":')[1].split(',"')[0]
                heartCount = response.split('"heartCount":')[1].split(',"')[0]
                videoCount = response.split('"videoCount":')[1].split(',"')[0]
                # return {'live':True,'uid': uid, 'nickName': nick_name, 'uniqueId': uniqueId} 
                return {'live':True,'uid': uid, 'nickName': nick_name, 'uniqueId': uniqueId, 'followerCount':followerCount,'followingCount':followingCount,'heartCount':heartCount,'videoCount':videoCount} 
            
            # "uid":"7210032702374806554","nickName":"Dang Dinh Toan","signature":"","uniqueId":"tournescloneuser01", RSP
        except Exception as e:
            return {'live':False} 
        
    def publicLoveAPI(self):
        user_agent_mb = 'com.ss.android.ugc.trill/995 (Linux; U; Android 7.1.2; en_US; ASUS_Z01QD; Build/N2G48H; Cronet/77.0.3844.0)'
        url_open_tym = 'https://api16-normal-c-useast1a.tiktokv.com/aweme/v1/user/set/settings/?field=favorite_permission&value=0&os_api=25&device_type=ASUS_Z01QD&ssmix=a&manifest_version_code=995&dpi=240&carrier_region=VN&uoo=0&region=US&uuid=841023267387228&app_skin=white&app_name=trill&version_name=9.9.5&timezone_offset=-21600&ts=1664613812&ab_version=9.9.5&residence=VN&pass-route=1&pass-region=1&is_my_cn=0&current_region=VN&ac2=wifi&app_type=normal&ac=wifi&channel=googleplay&update_version_code=9950&_rticket=1664613812700&device_platform=android&iid=7149451393853081346&build_number=9.9.5&locale=en&op_region=VN&version_code=995&timezone_name=America%2FChicago&openudid=06ca0f001df50f81&sys_region=US&device_id=7149449994248144386&app_language=en&resolution=720*1280&device_brand=Asus&language=en&os_version=7.1.2&aid=1180&mcc_mnc=45204'
        url_open_tym_us = 'https://api16-normal-useast5.us.tiktokv.com/aweme/v1/user/set/settings/?field=favorite_permission&value=0&os_api=25&device_type=ASUS_Z01QD&ssmix=a&manifest_version_code=995&dpi=240&carrier_region=VN&uoo=0&region=US&uuid=841023267387228&app_skin=white&app_name=trill&version_name=9.9.5&timezone_offset=-21600&ts=1664613812&ab_version=9.9.5&residence=VN&pass-route=1&pass-region=1&is_my_cn=0&current_region=VN&ac2=wifi&app_type=normal&ac=wifi&channel=googleplay&update_version_code=9950&_rticket=1664613812700&device_platform=android&iid=7149451393853081346&build_number=9.9.5&locale=en&op_region=VN&version_code=995&timezone_name=America%2FChicago&openudid=06ca0f001df50f81&sys_region=US&device_id=7149449994248144386&app_language=en&resolution=720*1280&device_brand=Asus&language=en&os_version=7.1.2&aid=1180&mcc_mnc=45204'

        headers_tym = {
            'Cookie': self.cookie,
            'User-Agent': user_agent_mb,
            'sdk-version':'1',
            'x-khronos':'1664613812',
            'x-ss-req-ticket':'1664613812698',
            'x-gorgon':'040120b94001dd2be7abff1f0507e32b7a9570ab0d00833543d9',
        }
        # try:
        if 'store-country-code=us' in self.cookie:
            requests.get(url_open_tym_us, headers=headers_tym).text
        else:
            requests.get(url_open_tym, headers=headers_tym).text
        
def checkLiveUID(username: str):
    try:
        response = requests.get(f'https://now.tiktok.com/@{username}',headers = {'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9'}).text
        # "uid":"7210032702374806554","nickName":"Dang Dinh Toan","signature":"","uniqueId":"tournescloneuser01", RSP
        
        uid = response.split('{"id":"')[1].split('","shortId":"')[0]
        nick_name = response.split('","nickname":"')[1].split('","avatarLarger"')[0]
        uniqueId = response.split('","shortId":"","uniqueId":"')[1].split('","nickname"')[0]
        return {'live':True,'uid': uid, 'nickName': nick_name, 'uniqueId': uniqueId} 
    except: return {'live':False} 
    
def checkIdVideo(username: str):
    try:
        
        try:
            rsp = requests.get(f'https://www.tiktok.com/@{username}',headers = {'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9'}).text
            idVideo = rsp.split('"user-post":{"list":["')[1].split('","')[0]
            if len(idVideo) > 25:
                idVideo = rsp.split('"user-post":{"list":["')[1].split('"]')[0]
        except:
            rsp = requests.get(f'https://us.tiktok.com/@{username}',headers={'authority': 'us.tiktok.com','accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7','accept-language': 'vi-VN,vi;q=0.9','cache-control': 'max-age=0','sec-ch-ua': '"Chromium";v="118", "Google Chrome";v="118", "Not=A?Brand";v="99"','sec-ch-ua-mobile': '?0','sec-ch-ua-platform': '"Windows"','sec-fetch-dest': 'document','sec-fetch-mode': 'navigate','sec-fetch-site': 'none','sec-fetch-user': '?1','upgrade-insecure-requests': '1','user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',}).text
            idVideo = rsp.split('"user-post":{"list":["')[1].split('","')[0]
            if len(idVideo) > 25:
                idVideo = rsp.split('"user-post":{"list":["')[1].split('"]')[0]
        return idVideo
    except Exception as e:
        
        # logging.basicConfig(filename=errorlog, level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')
        # logging.exception("Exception occurred: {}".format(e))
        return 0