from library import *
class TDS:
    def __init__(self, username, password) -> None:
        self.username, self.password = username, password
        self.s = requests.Session()
        self.tokenUser = ''
        
    def addProxy(self, proxy: str):
        try:
            if len(proxy.split(':')) >= 4:
                parts = proxy.split(':')
                iport = ":".join(parts[:2])
                userpass = ":".join(parts[2:])
                self.proxyDict = {
                    'https': 'http://{}@{}'.format(userpass,iport),
                    'http': 'http://{}@{}'.format(userpass,userpass)
                    }
            elif len(proxy.split(':')) >= 2:
                proxy_url = 'http://{}'.format(proxy)
                self.proxyDict   = { 
                "http"  : proxy_url, 
                "https" : proxy_url
            }
            else:
                self.proxyDict   = ''
            return True
        except: return False

    def loginTDS(self):
        try:
            self.cookies = self.s.request('POST','https://traodoisub.com/scr/login.php' ,data={'username': self.username,'password': self.password},timeout=15, proxies=self.proxyDict).cookies
            rsp = self.s.request('GET',"https://traodoisub.com/home/",cookies= self.cookies,timeout=15, proxies=self.proxyDict)
            if 'soduchinh' in rsp.text:
                xu, self.tokenUser = self.getUser()
                return {'status': "success", 'data': {'user': self.username, 'xu': xu} }
            else:
                return {'status': "error", 'mess': "Sai tài khoản hoặc mật khẩu or DDOS"}
        except:
            return {'status': "error", 'mess': "Connect timeout"}
        
    def getXuTDS(self):
        try:
            xu, self.tokenUser = self.getUser()
            return  {'user': self.username, 'xu': xu}
        except Exception as e:
            return {'status': 'error', 'mess': 'Có lỗi xảy ra khi kiểm tra xu !'}
        
    def getUser(self):
        try:
            info = self.s.get('https://traodoisub.com/view/setting/load.php',cookies=self.cookies,timeout=15, proxies=self.proxyDict).json()
            self.tokenUser = info['tokentds']
            return info['xu'], info['tokentds']    
        except Exception as e:
            return 0, self.tokenUser
        
    def activeIdRunToken(self, id: int):
        for t in range(3):
            try:
                rsp = requests.get(f'https://traodoisub.com/api/?fields=tiktok_run&id={id}&access_token={self.tokenUser}',timeout=15, proxies=self.proxyDict)
                if 'success' in rsp.text: return {'status': "success", 'mess': rsp.json()['data']['msg']} 
                elif 'Access token không hợp lệ! xin thử lại' in rsp.text:
                    self.getuser()
                else:return {'status': "error", 'mess': rsp.json()['error']} 
            except Exception as e:pass
            time.sleep(random.randint(3,6))
        return {'status': 'error', 'mess': 'Có lỗi xảy ra khi Active Token !'}
    
    def cauHinh(self, username, captcha):
        try:
            response = self.s.request('POST','https://traodoisub.com/scr/tiktok_add.php',cookies=  self.cookies,data={"idfb":username,'g-recaptcha-response':captcha}, proxies=self.proxyDict, timeout=15)
            if 'success' in response.text:
                return {'status': "success", 'mess': "Cấu hình nick thành công"}
            else:
                return {'status': "error", 'mess': response.json()['error']}
        except Exception as e:
            return {'status': 'error', 'mess': 'Có lỗi xảy ra khi Cấu hình !'}
        
    def datNik(self, iddat):
        try:
            response = self.s.request('POST','https://traodoisub.com/scr/tiktok_datnick.php', data={ 'iddat':  iddat}, cookies= self.cookies,timeout=15, proxies=self.proxyDict)
            if '1' in response.text:
                return {'status': "success", 'mess': "Đặt nick thành công"}
            else:
                return {'status': "error", 'mess': response.text}
        except Exception as e:
            return {'status': 'error', 'mess': 'Có lỗi xảy ra khi đặt nick !'}
        
    def getJob(self,type):
        try:
            if type == 'love':
                a=requests.get(f'https://traodoisub.com/api/?fields=tiktok_like&access_token={self.tokenUser}', proxies=self.proxyDict, timeout=15).json()
                print(a)
                return a
            if type == 'follow':
                a=requests.get(f'https://traodoisub.com/api/?fields=tiktok_follow&access_token={self.tokenUser}', proxies=self.proxyDict, timeout=15).json()
                return a
            if type == 'cmt':
                rsp = self.s.request('GET','https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php',headers=self.headers,timeout=15, proxies=self.proxyDict)
                return rsp.json()
        except Exception as e:
            logging.basicConfig(filename=errorlog, level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')
            logging.exception("Lỗi lấy Job TDS")
            return {'status': 'error', 'mess': 'Có lỗi xảy ra khi lấy nhiệm vụ !'}
        
    def postCache(self, type, id):
        for t in range(3):
            try:
                if type == 'love':
                    type = 'like'
                data = {
                    'id': id,
                    'type': type.lower(),
                }
                
                check=requests.get(f'https://traodoisub.com/api/coin/?type=TIKTOK_{type.upper()}_CACHE&id={id}&access_token={self.tokenUser}',proxies=self.proxyDict, timeout=15)
                if 'Access token không hợp lệ! xin thử lại' in str(check.json()):self.getuser()
                return check.json()
            except Exception as e:
                logging.basicConfig(filename=errorlog, level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')
                logging.exception("Lỗi gửi duyệt Job TDS")
                
        try:
            return {'status': 'error', 'mess': 'Có lỗi xảy ra khi gửi nhiệm vụ !', 'id': id, 'type': type, 'mess': check.text}
        except:return {'status': 'error', 'mess': 'Có lỗi xảy ra khi gửi nhiệm vụ !', 'id': id, 'type': type, 'mess': ''}
    
    def getXuJob(self,type):
        try:
            if type == 'love':
                type = 'LIKE'
            get_tt=requests.get(f'https://traodoisub.com/api/coin/?type=TIKTOK_{type.upper()}&id=TIKTOK_{type.upper()}_API&access_token={self.tokenUser}',proxies=self.proxyDict, timeout=15)
            if 'Access token không hợp lệ! xin thử lại' in get_tt.json():self.getuser()
            if "success" in get_tt.text and "Xu" in get_tt.text:
                return {'status': 'success', 'mess': get_tt.json()['data']['xu_them'], 'msg':get_tt.json()['data']['msg']}
            else:
                return {'status': 'error', 'mess': get_tt.json()['msg'], 'msg': get_tt.json()['msg'] }
        except Exception as e:
            logging.basicConfig(filename=errorlog, level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')
            logging.exception("Lỗi nhận xu TDS")
            return {'status': 'error', 'mess': 'Có lỗi xảy ra khi nhận xu !', 'msg':'Có lỗi xảy ra khi nhận xu !'}