from Core import *
from src.modules import *

class Ui_Import(object):
    gripSize = 16; grips = []; _old_pos = None
    def setupUi(self, Import, folderName):
        self.folderName = folderName
        Import.setObjectName("Import")
        Import.setFixedSize(1172, 636)
        Import.setWindowIcon(QtGui.QIcon('icon.ico'))
        Import.setAttribute(Qt.WA_TranslucentBackground)
        Import.setWindowFlags(Qt.FramelessWindowHint)
        self.stylesheet = QtWidgets.QWidget(Import)
        self.stylesheet.setStyleSheet("* {\n"
"    background-color: transparent;\n"
"    background: none;\n"
"    border: none;\n"
"    padding: 0;\n"
"    margin: 0;\n"
"    font: 10pt \"Segoe UI\";\n"
"    background: no-repeat;\n"
"    color: #fff;\n"
"}\n"
"\n"
"QWidget {\n"
"    color: rgb(221, 221, 221);\n"
"    font: 10pt \"Segoe UI\";\n"
"}\n"
"\n"
"#MainPages {\n"
"    border-radius: 5px;\n"
"    background-color: #2c313c;\n"
"}\n"
"\n"
"#farmeleftMenu QPushButton {\n"
"    text-align: left;\n"
"    padding: 3px 17px;\n"
"    padding-right: 75px;\n"
"}\n"
"\n"
"\n"
"#leftMenuSettings {\n"
"    background-color: #343b48;\n"
"}\n"
"\n"
"#frameLeftTop {\n"
"    background-color: #3c4454;\n"
"\n"
"}\n"
"\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"Right Pages */\n"
"#fullrightPages {\n"
"    background-color: #272c36;\n"
"}\n"
"\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"QTableWidget */\n"
"QTableWidget {\n"
"    background-color: #343b48;\n"
"    color: white;\n"
"    padding: 5;\n"
"    border: none;\n"
"\n"
"}\n"
"\n"
"QTableWidget::item {\n"
"    background-color: rgba(68, 119, 170, 125);\n"
"}\n"
"\n"
"QHeaderView {\n"
"    border-top-left-radius: 5px;\n"
"    border-top-right-radius: 5px;\n"
"    background-color: #1b1e23;\n"
"    padding: 5px 5px;\n"
"    color: white;\n"
"\n"
"    font-family: \'Montserrat\', sans-serif;\n"
"\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    background-color: #1b1e23;\n"
"    border-style: none;\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"LineEdit */\n"
"QLineEdit {\n"
"    background-color: #1b1e23;\n"
"    border-radius: 5px;\n"
"    border: 2px solid rgb(33, 37, 43);\n"
"    padding-left: 10px;\n"
"    selection-color: rgb(255, 255, 255);\n"
"    selection-background-color: rgb(255, 121, 198);\n"
"}\n"
"\n"
"QLineEdit:hover {\n"
"    border: 2px solid rgb(64, 71, 88);\n"
"}\n"
"\n"
"QLineEdit:focus {\n"
"    border: 2px solid rgb(91, 101, 124);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"PlainTextEdit */\n"
"QPlainTextEdit {\n"
"    background-color: rgb(27, 29, 35);\n"
"    border-radius: 5px;\n"
"    padding: 10px;\n"
"    selection-color: rgb(255, 255, 255);\n"
"    selection-background-color: rgb(255, 121, 198);\n"
"}\n"
"\n"
"QPlainTextEdit QScrollBar:vertical {\n"
"    width: 8px;\n"
"}\n"
"\n"
"QPlainTextEdit QScrollBar:horizontal {\n"
"    height: 8px;\n"
"}\n"
"\n"
"QPlainTextEdit:hover {\n"
"    border: 2px solid rgb(64, 71, 88);\n"
"}\n"
"\n"
"QPlainTextEdit:focus {\n"
"    border: 2px solid rgb(91, 101, 124);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"ScrollBars */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    background: rgb(52, 59, 72);\n"
"    height: 8px;\n"
"    margin: 0px 21px 0 21px;\n"
"    border-radius: 0px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background: rgb(189, 147, 249);\n"
"    min-width: 25px;\n"
"    border-radius: 4px\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal {\n"
"    border: none;\n"
"    background: rgb(55, 63, 77);\n"
"    width: 20px;\n"
"    border-top-right-radius: 4px;\n"
"    border-bottom-right-radius: 4px;\n"
"    subcontrol-position: right;\n"
"    subcontrol-origin: margin;\n"
"}\n"
"\n"
"QScrollBar::sub-line:horizontal {\n"
"    border: none;\n"
"    background: rgb(55, 63, 77);\n"
"    width: 20px;\n"
"    border-top-left-radius: 4px;\n"
"    border-bottom-left-radius: 4px;\n"
"    subcontrol-position: left;\n"
"    subcontrol-origin: margin;\n"
"}\n"
"\n"
"QScrollBar::up-arrow:horizontal,\n"
"QScrollBar::down-arrow:horizontal {\n"
"    background: none;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}\n"
"\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    background: rgb(52, 59, 72);\n"
"    width: 8px;\n"
"    margin: 21px 0 21px 0;\n"
"    border-radius: 0px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background: rgb(189, 147, 249);\n"
"    min-height: 25px;\n"
"    border-radius: 4px\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical {\n"
"    border: none;\n"
"    background: rgb(55, 63, 77);\n"
"    height: 20px;\n"
"    border-bottom-left-radius: 4px;\n"
"    border-bottom-right-radius: 4px;\n"
"    subcontrol-position: bottom;\n"
"    subcontrol-origin: margin;\n"
"}\n"
"\n"
"QScrollBar::sub-line:vertical {\n"
"    border: none;\n"
"    background: rgb(55, 63, 77);\n"
"    height: 20px;\n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    subcontrol-position: top;\n"
"    subcontrol-origin: margin;\n"
"}\n"
"\n"
"QScrollBar::up-arrow:vertical,\n"
"QScrollBar::down-arrow:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"CheckBox */\n"
"QCheckBox::indicator {\n"
"    border: 3px solid rgb(52, 59, 72);\n"
"    width: 15px;\n"
"    height: 15px;\n"
"    border-radius: 5px;\n"
"    background: rgb(44, 49, 60);\n"
"}\n"
"\n"
"QCheckBox::indicator:hover {\n"
"    border: 3px solid rgb(58, 66, 81);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    background: 3px solid rgb(52, 59, 72);\n"
"    border: 3px solid rgb(52, 59, 72);\n"
"    background-image: url(:/icon/images/icons/cil-check-alt.png);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"RadioButton */\n"
"QRadioButton::indicator {\n"
"    border: 3px solid rgb(52, 59, 72);\n"
"    width: 15px;\n"
"    height: 15px;\n"
"    border-radius: 10px;\n"
"    background: rgb(44, 49, 60);\n"
"}\n"
"\n"
"QRadioButton::indicator:hover {\n"
"    border: 3px solid rgb(58, 66, 81);\n"
"}\n"
"\n"
"QRadioButton::indicator:checked {\n"
"    background: 3px solid rgb(94, 106, 130);\n"
"    border: 3px solid rgb(52, 59, 72);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"ComboBox */\n"
"QComboBox {\n"
"    background-color: rgb(27, 29, 35);\n"
"    border-radius: 5px;\n"
"    border: 2px solid rgb(33, 37, 43);\n"
"    padding: 5px;\n"
"    padding-left: 10px;\n"
"\n"
"}\n"
"\n"
"QComboBox:hover {\n"
"    border: 2px solid rgb(64, 71, 88);\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: padding;\n"
"    subcontrol-position: top right;\n"
"    width: 25px;\n"
"    border-left-width: 3px;\n"
"    border-left-color: rgba(39, 44, 54, 150);\n"
"    border-left-style: solid;\n"
"    border-top-right-radius: 3px;\n"
"    border-bottom-right-radius: 3px;\n"
"    background-image: url(:/icon/images/icons/cil-arrow-bottom.png);\n"
"    background-position: center;\n"
"    background-repeat: no-reperat;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView {\n"
"    color: rgb(255, 121, 198);\n"
"    background-color: rgb(33, 37, 43);\n"
"    padding: 10px;\n"
"    selection-background-color: rgb(39, 44, 54);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"Sliders */\n"
"QSlider::groove:horizontal {\n"
"    border-radius: 5px;\n"
"    height: 10px;\n"
"    margin: 0px;\n"
"    background-color: rgb(52, 59, 72);\n"
"}\n"
"\n"
"QSlider::groove:horizontal:hover {\n"
"    background-color: rgb(55, 62, 76);\n"
"}\n"
"\n"
"QSlider::handle:horizontal {\n"
"    background-color: rgb(189, 147, 249);\n"
"    border: none;\n"
"    height: 10px;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"    border-radius: 5px;\n"
"}\n"
"\n"
"QSlider::handle:horizontal:hover {\n"
"    background-color: rgb(195, 155, 255);\n"
"}\n"
"\n"
"QSlider::handle:horizontal:pressed {\n"
"    background-color: rgb(255, 121, 198);\n"
"}\n"
"\n"
"QSlider::groove:vertical {\n"
"    border-radius: 5px;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"    background-color: rgb(52, 59, 72);\n"
"}\n"
"\n"
"QSlider::groove:vertical:hover {\n"
"    background-color: rgb(55, 62, 76);\n"
"}\n"
"\n"
"QSlider::handle:vertical {\n"
"    background-color: rgb(189, 147, 249);\n"
"    border: none;\n"
"    height: 10px;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"    border-radius: 5px;\n"
"}\n"
"\n"
"QSlider::handle:vertical:hover {\n"
"    background-color: rgb(195, 155, 255);\n"
"}\n"
"\n"
"QSlider::handle:vertical:pressed {\n"
"    background-color: rgb(255, 121, 198);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"CommandLinkButton */\n"
"QCommandLinkButton {\n"
"    color: rgb(255, 121, 198);\n"
"    border-radius: 5px;\n"
"    padding: 5px;\n"
"    color: rgb(255, 170, 255);\n"
"}\n"
"\n"
"QCommandLinkButton:hover {\n"
"    color: rgb(255, 170, 255);\n"
"    background-color: rgb(44, 49, 60);\n"
"}\n"
"\n"
"QCommandLinkButton:pressed {\n"
"    color: rgb(189, 147, 249);\n"
"    background-color: rgb(52, 58, 71);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"QGroupBox */\n"
"\n"
"QGroupBox {\n"
"    border: 1px solid rgb(221, 221, 221);\n"
"}\n"
"\n"
"QGroupBox {\n"
"    border: 1px solid #343b48;\n"
"    margin-top: 8px;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 4px;\n"
"    padding: 0px 5px 0px 5px;\n"
"}\n"
"\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"QSpinBox */\n"
"\n"
"QSpinBox {\n"
"    background-color: #1b1e23;\n"
"}\n"
"\n"
"QSpinBox::down-button {\n"
"    border-top-left-radius: 3px;\n"
"    border-bottom-left-radius: 3px;\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: center left;\n"
"    image: url(:/icon/images/icons/cil-arrow-bottom.png);\n"
"    background-color: #2c313c;\n"
"    left: 1px;\n"
"    height: 20px;\n"
"    width: 20px;\n"
"}\n"
"\n"
"QSpinBox::up-button {\n"
"    border-top-right-radius: 3px;\n"
"    border-bottom-right-radius: 3px;\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: center right;\n"
"    image: url(:/icon/images/icons/cil-arrow-top.png);\n"
"    background-color: #2c313c;\n"
"    left: 1px;\n"
"    height: 20px;\n"
"    width: 20px;\n"
"}\n"
"\n"
"QSpinBox::up-button:pressed {\n"
"    background-color: #6c99f4;\n"
"}\n"
"\n"
"QSpinBox::down-button:pressed {\n"
"    background-color: #6c99f4;\n"
"}\n"
"\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"QTabWidget */\n"
"\n"
"QTabWidget::pane {\n"
"    border: 1px solid #272c36;\n"
"    background: rgb(245, 245, 245);\n"
"    right:1px;\n"
"}\n"
"\n"
"QTabBar::tab {\n"
"    text-align: left;\n"
"    background: #343b48;\n"
"    border: 1px solid #272c36;\n"
"    padding: 5 3;\n"
"    width: 115;\n"
"}\n"
"\n"
"QTabBar::tab:selected {\n"
"    background: #2c313c;\n"
"    margin-bottom: -1px;\n"
"}")
        self.stylesheet.setObjectName("stylesheet")
        self.gridLayout_6 = QtWidgets.QGridLayout(self.stylesheet)
        self.gridLayout_6.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_6.setHorizontalSpacing(0)
        self.gridLayout_6.setObjectName("gridLayout_6")
        self.MainPages = QtWidgets.QFrame(self.stylesheet)
        self.MainPages.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.MainPages.setFrameShadow(QtWidgets.QFrame.Raised)
        self.MainPages.setObjectName("MainPages")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.MainPages)
        self.horizontalLayout.setContentsMargins(6, 6, 6, 6)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.frame = QtWidgets.QFrame(self.MainPages)
        self.frame.setStyleSheet("background-color: #272c36;")
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.Title = QtWidgets.QFrame(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.Title.sizePolicy().hasHeightForWidth())
        self.Title.setSizePolicy(sizePolicy)
        self.Title.setStyleSheet("*{\n"
"padding:3 0;\n"
"background-color:#343b48;\n"
"}\n"
"\n"
"QFrame #rightTitle{\n"
"\n"
"border-bottom: 2px solid #3c4454;\n"
"\n"
"}\n"
"\n"
"\n"
"")
        self.Title.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.Title.setFrameShadow(QtWidgets.QFrame.Raised)
        self.Title.setObjectName("Title")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.Title)
        self.horizontalLayout_3.setContentsMargins(3, 0, 0, 0)
        self.horizontalLayout_3.setSpacing(0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.leftTitle = QtWidgets.QFrame(self.Title)
        self.leftTitle.setStyleSheet("")
        self.leftTitle.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.leftTitle.setFrameShadow(QtWidgets.QFrame.Raised)
        self.leftTitle.setObjectName("leftTitle")
        self.gridLayout_3 = QtWidgets.QGridLayout(self.leftTitle)
        self.gridLayout_3.setContentsMargins(5, 0, 0, 0)
        self.gridLayout_3.setSpacing(0)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.btn_title = QtWidgets.QPushButton(self.leftTitle)
        self.btn_title.setStyleSheet("font:bold;")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(".\\images/images/import.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_title.setIcon(icon)
        self.btn_title.setIconSize(QtCore.QSize(24, 24))
        self.btn_title.setObjectName("btn_title")
        self.gridLayout_3.addWidget(self.btn_title, 0, 0, 1, 1, QtCore.Qt.AlignLeft)
        self.horizontalLayout_3.addWidget(self.leftTitle)
        self.rightButtons = QtWidgets.QFrame(self.Title)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.rightButtons.sizePolicy().hasHeightForWidth())
        self.rightButtons.setSizePolicy(sizePolicy)
        self.rightButtons.setStyleSheet("\n"
"QPushButton:hover {\n"
"    background-color: #3c4454;\n"
"    border-radius:5px;\n"
"\n"
"\n"
"}\n"
"QPushButton:pressed { \n"
"    background-color: #2c313c;\n"
"     border-radius:5px;\n"
"}\n"
"\n"
"\n"
"\n"
"")
        self.rightButtons.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.rightButtons.setFrameShadow(QtWidgets.QFrame.Raised)
        self.rightButtons.setObjectName("rightButtons")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.rightButtons)
        self.horizontalLayout_2.setContentsMargins(0, 0, 6, 0)
        self.horizontalLayout_2.setSpacing(3)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.minimizeAppBtn = QtWidgets.QPushButton(self.rightButtons)
        self.minimizeAppBtn.setStyleSheet("")
        self.minimizeAppBtn.setText("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(".\\images/icons/icon_minimize.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.minimizeAppBtn.setIcon(icon1)
        self.minimizeAppBtn.setIconSize(QtCore.QSize(28, 24))
        self.minimizeAppBtn.setObjectName("minimizeAppBtn")
        self.horizontalLayout_2.addWidget(self.minimizeAppBtn)
        self.maximizeRestoreAppBtn = QtWidgets.QPushButton(self.rightButtons)
        self.maximizeRestoreAppBtn.setStyleSheet("")
        self.maximizeRestoreAppBtn.setText("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(".\\images/icons/icon_maximize.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.maximizeRestoreAppBtn.setIcon(icon2)
        self.maximizeRestoreAppBtn.setIconSize(QtCore.QSize(28, 24))
        self.maximizeRestoreAppBtn.setObjectName("maximizeRestoreAppBtn")
        self.horizontalLayout_2.addWidget(self.maximizeRestoreAppBtn)
        self.closeAppBtn = QtWidgets.QPushButton(self.rightButtons)
        self.closeAppBtn.setStyleSheet("\n"
"QPushButton:pressed { \n"
"background-color: #ff5555;\n"
" border-style: solid; border-radius: 4px; \n"
"}\n"
"")
        self.closeAppBtn.setText("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(".\\images/icons/icon_close.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.closeAppBtn.setIcon(icon3)
        self.closeAppBtn.setIconSize(QtCore.QSize(28, 24))
        self.closeAppBtn.setObjectName("closeAppBtn")
        self.horizontalLayout_2.addWidget(self.closeAppBtn)
        self.horizontalLayout_3.addWidget(self.rightButtons)
        self.verticalLayout.addWidget(self.Title)
        self.bodyPages = QtWidgets.QFrame(self.frame)
        self.bodyPages.setStyleSheet("")
        self.bodyPages.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.bodyPages.setFrameShadow(QtWidgets.QFrame.Raised)
        self.bodyPages.setObjectName("bodyPages")
        self.gridLayout_4 = QtWidgets.QGridLayout(self.bodyPages)
        self.gridLayout_4.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_4.setSpacing(0)
        self.gridLayout_4.setObjectName("gridLayout_4")
        self.frameBody = QtWidgets.QFrame(self.bodyPages)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frameBody.sizePolicy().hasHeightForWidth())
        self.frameBody.setSizePolicy(sizePolicy)
        self.frameBody.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.frameBody.setStyleSheet("*{\n"
" background-color: #2c313c;\n"
"}\n"
"QSpinBox {\n"
"     background-color: rgb(34, 38, 47);\n"
"}\n"
"\n"
"QFrame #frameDSTK{\n"
"border-top:2px solid #5fbc5a;\n"
"background-color: #111111;\n"
"padding:3 5 1 5;\n"
"}\n"
"QLabel {\n"
"    background-color: transparent;\n"
"}")
        self.frameBody.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frameBody.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frameBody.setObjectName("frameBody")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frameBody)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.frameDSTK = QtWidgets.QFrame(self.frameBody)
        self.frameDSTK.setStyleSheet("")
        self.frameDSTK.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frameDSTK.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frameDSTK.setObjectName("frameDSTK")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.frameDSTK)
        self.horizontalLayout_4.setContentsMargins(0, 3, 0, -1)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.label = QtWidgets.QLabel(self.frameDSTK)
        self.label.setObjectName("label")
        self.horizontalLayout_4.addWidget(self.label)
        self.label_2 = QtWidgets.QLabel(self.frameDSTK)
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_4.addWidget(self.label_2, 0, QtCore.Qt.AlignRight)
        self.verticalLayout_2.addWidget(self.frameDSTK)
        self.textImport = QtWidgets.QPlainTextEdit(self.frameBody)
        self.textImport.setStyleSheet("*{\n"
"    background-color:#343b48;\n"
"}")
        self.textImport.setObjectName("textImport")
        self.verticalLayout_2.addWidget(self.textImport)
        self.frameCbox = QtWidgets.QFrame(self.frameBody)
        self.frameCbox.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frameCbox.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frameCbox.setObjectName("frameCbox")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.frameCbox)
        self.horizontalLayout_5.setContentsMargins(0, -1, 0, -1)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.cb1 = QtWidgets.QComboBox(self.frameCbox)
        font = QtGui.QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(10)
        font.setBold(False)
        font.setItalic(False)
        font.setWeight(50)
        font.setKerning(True)
        self.cb1.setFont(font)
        self.cb1.setObjectName("cb1")
        self.cb1.addItem("")
        self.cb1.addItem("")
        self.cb1.addItem("")
        self.cb1.addItem("")
        self.cb1.addItem("")
        self.cb1.addItem("")
        self.cb1.addItem("")
        self.cb1.addItem("")
        self.cb1.addItem("")
        self.cb1.addItem("")
        self.cb1.setItemText(9, "")
        self.horizontalLayout_5.addWidget(self.cb1)
        self.cb2 = QtWidgets.QComboBox(self.frameCbox)
        self.cb2.setObjectName("cb2")
        self.cb2.addItem("")
        self.cb2.addItem("")
        self.cb2.addItem("")
        self.cb2.addItem("")
        self.cb2.addItem("")
        self.cb2.addItem("")
        self.cb2.addItem("")
        self.cb2.addItem("")
        self.cb2.addItem("")
        self.cb2.addItem("")
        self.cb2.setItemText(9, "")
        self.horizontalLayout_5.addWidget(self.cb2)
        self.cb3 = QtWidgets.QComboBox(self.frameCbox)
        self.cb3.setObjectName("cb3")
        self.cb3.addItem("")
        self.cb3.addItem("")
        self.cb3.addItem("")
        self.cb3.addItem("")
        self.cb3.addItem("")
        self.cb3.addItem("")
        self.cb3.addItem("")
        self.cb3.addItem("")
        self.cb3.addItem("")
        self.cb3.addItem("")
        self.cb3.setItemText(9, "")
        self.horizontalLayout_5.addWidget(self.cb3)
        self.cb4 = QtWidgets.QComboBox(self.frameCbox)
        self.cb4.setObjectName("cb4")
        self.cb4.addItem("")
        self.cb4.addItem("")
        self.cb4.addItem("")
        self.cb4.addItem("")
        self.cb4.addItem("")
        self.cb4.addItem("")
        self.cb4.addItem("")
        self.cb4.addItem("")
        self.cb4.addItem("")
        self.cb4.addItem("")
        self.cb4.setItemText(9, "")
        self.horizontalLayout_5.addWidget(self.cb4)
        self.cb5 = QtWidgets.QComboBox(self.frameCbox)
        self.cb5.setObjectName("cb5")
        self.cb5.addItem("")
        self.cb5.addItem("")
        self.cb5.addItem("")
        self.cb5.addItem("")
        self.cb5.addItem("")
        self.cb5.addItem("")
        self.cb5.addItem("")
        self.cb5.addItem("")
        self.cb5.addItem("")
        self.cb5.addItem("")
        self.cb5.setItemText(9, "")
        self.horizontalLayout_5.addWidget(self.cb5)
        self.cb6 = QtWidgets.QComboBox(self.frameCbox)
        self.cb6.setObjectName("cb6")
        self.cb6.addItem("")
        self.cb6.addItem("")
        self.cb6.addItem("")
        self.cb6.addItem("")
        self.cb6.addItem("")
        self.cb6.addItem("")
        self.cb6.addItem("")
        self.cb6.addItem("")
        self.cb6.addItem("")
        self.cb6.addItem("")
        self.cb6.setItemText(9, "")
        self.horizontalLayout_5.addWidget(self.cb6)
        self.cb7 = QtWidgets.QComboBox(self.frameCbox)
        self.cb7.setObjectName("cb7")
        self.cb7.addItem("")
        self.cb7.addItem("")
        self.cb7.addItem("")
        self.cb7.addItem("")
        self.cb7.addItem("")
        self.cb7.addItem("")
        self.cb7.addItem("")
        self.cb7.addItem("")
        self.cb7.addItem("")
        self.cb7.addItem("")
        self.cb7.setItemText(9, "")
        self.horizontalLayout_5.addWidget(self.cb7)
        self.cb8 = QtWidgets.QComboBox(self.frameCbox)
        self.cb8.setObjectName("cb8")
        self.cb8.addItem("")
        self.cb8.addItem("")
        self.cb8.addItem("")
        self.cb8.addItem("")
        self.cb8.addItem("")
        self.cb8.addItem("")
        self.cb8.addItem("")
        self.cb8.addItem("")
        self.cb8.addItem("")
        self.cb8.addItem("")
        self.cb8.setItemText(9, "")
        self.horizontalLayout_5.addWidget(self.cb8)
        self.cb9 = QtWidgets.QComboBox(self.frameCbox)
        self.cb9.setObjectName("cb9")
        self.cb9.addItem("")
        self.cb9.addItem("")
        self.cb9.addItem("")
        self.cb9.addItem("")
        self.cb9.addItem("")
        self.cb9.addItem("")
        self.cb9.addItem("")
        self.cb9.addItem("")
        self.cb9.addItem("")
        self.cb9.addItem("")
        self.cb9.setItemText(9, "")
        self.horizontalLayout_5.addWidget(self.cb9)
        self.verticalLayout_2.addWidget(self.frameCbox)
        self.btnSave = QtWidgets.QPushButton(self.frameBody)
        self.btnSave.setStyleSheet("* {\n"
"    background-color: #1b1e23;\n"
"    border-radius:3px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    height: 40px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #272c36;\n"
"\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #1b1e23;\n"
"\n"
"}")
        self.btnSave.setObjectName("btnSave")
        self.verticalLayout_2.addWidget(self.btnSave)
        self.gridLayout_4.addWidget(self.frameBody, 0, 0, 1, 1)
        self.verticalLayout.addWidget(self.bodyPages)
        self.bottom = QtWidgets.QFrame(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.bottom.sizePolicy().hasHeightForWidth())
        self.bottom.setSizePolicy(sizePolicy)
        self.bottom.setStyleSheet("*{\n"
"    background-color: #181818;\n"
"    color:#5f8899;\n"
"    height:30px;\n"
"}\n"
"\n"
"")
        self.bottom.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.bottom.setFrameShadow(QtWidgets.QFrame.Raised)
        self.bottom.setObjectName("bottom")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.bottom)
        self.horizontalLayout_10.setContentsMargins(5, 5, 5, 5)
        self.horizontalLayout_10.setSpacing(0)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.lblCoppyright = QtWidgets.QLabel(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lblCoppyright.sizePolicy().hasHeightForWidth())
        self.lblCoppyright.setSizePolicy(sizePolicy)
        self.lblCoppyright.setObjectName("lblCoppyright")
        self.horizontalLayout_10.addWidget(self.lblCoppyright)
        self.lblPyTournes1 = QtWidgets.QLabel(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lblPyTournes1.sizePolicy().hasHeightForWidth())
        self.lblPyTournes1.setSizePolicy(sizePolicy)
        self.lblPyTournes1.setStyleSheet("QLabel {\n"
"color:#5fbc5a;\n"
"font:bold;\n"
"}\n"
"QLabel:hover{\n"
"    color:#ff0000;\n"
"}\n"
"")
        self.lblPyTournes1.setObjectName("lblPyTournes1")
        self.horizontalLayout_10.addWidget(self.lblPyTournes1)
        self.label_3 = QtWidgets.QLabel(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
        self.label_3.setSizePolicy(sizePolicy)
        self.label_3.setStyleSheet("")
        self.label_3.setObjectName("label_3")
        self.horizontalLayout_10.addWidget(self.label_3)
        self.lblPyTournes2 = QtWidgets.QLabel(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lblPyTournes2.sizePolicy().hasHeightForWidth())
        self.lblPyTournes2.setSizePolicy(sizePolicy)
        self.lblPyTournes2.setStyleSheet("QLabel {\n"
"color:#5fbc5a;\n"
"font:bold;\n"
"}\n"
"QLabel:hover{\n"
"    color:#ff0000;\n"
"}\n"
"")
        self.lblPyTournes2.setObjectName("lblPyTournes2")
        self.horizontalLayout_10.addWidget(self.lblPyTournes2)
        self.label_5 = QtWidgets.QLabel(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_5.sizePolicy().hasHeightForWidth())
        self.label_5.setSizePolicy(sizePolicy)
        self.label_5.setObjectName("label_5")
        self.horizontalLayout_10.addWidget(self.label_5)
        self.lblPyTournes3 = QtWidgets.QLabel(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lblPyTournes3.sizePolicy().hasHeightForWidth())
        self.lblPyTournes3.setSizePolicy(sizePolicy)
        self.lblPyTournes3.setStyleSheet("QLabel {\n"
"color:#5fbc5a;\n"
"font:bold;\n"
"}\n"
"QLabel:hover{\n"
"    color:#ff0000;\n"
"}\n"
"")
        self.lblPyTournes3.setObjectName("lblPyTournes3")
        self.horizontalLayout_10.addWidget(self.lblPyTournes3)
        self.label_9 = QtWidgets.QLabel(self.bottom)
        self.label_9.setObjectName("label_9")
        self.horizontalLayout_10.addWidget(self.label_9)
        self.label_4 = QtWidgets.QLabel(self.bottom)
        self.label_4.setText("")
        self.label_4.setObjectName("label_4")
        self.horizontalLayout_10.addWidget(self.label_4)
        self.pushButton = QtWidgets.QPushButton(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton.sizePolicy().hasHeightForWidth())
        self.pushButton.setSizePolicy(sizePolicy)
        self.pushButton.setText("")
        self.pushButton.setObjectName("pushButton")
        self.horizontalLayout_10.addWidget(self.pushButton)
        self.verticalLayout.addWidget(self.bottom)
        self.horizontalLayout.addWidget(self.frame)
        self.gridLayout_6.addWidget(self.MainPages, 0, 0, 1, 1)
        Import.setCentralWidget(self.stylesheet)

        self.retranslateUi(Import)

        QtCore.QMetaObject.connectSlotsByName(Import)

    def retranslateUi(self, Import):
        _translate = QtCore.QCoreApplication.translate
        Import.setWindowTitle(_translate("Import", "PyTournes - IMPORT"))
        self.btn_title.setText(_translate("Import", "Import Page"))
        self.label.setText(_translate("Import", "Danh sách tài khoản ( 0 ):"))
        self.label_2.setText(_translate("Import", "Một tài khoản tương đương 1 dòng"))
        self.textImport.setPlaceholderText(_translate("Import", "Ví dụ có thể nhập các định dạng như phía dưới"))
        self.cb1.setItemText(0, _translate("Import", "UID"))
        self.cb1.setItemText(1, _translate("Import", "Password"))
        self.cb1.setItemText(2, _translate("Import", "Cookie"))
        self.cb1.setItemText(3, _translate("Import", "Mail"))
        self.cb1.setItemText(4, _translate("Import", "PassMail"))
        self.cb1.setItemText(5, _translate("Import", "UserCoin"))
        self.cb1.setItemText(6, _translate("Import", "PassCoin"))
        self.cb1.setItemText(7, _translate("Import", "Proxy"))
        self.cb1.setItemText(8, _translate("Import", "Useragent"))
        self.cb2.setItemText(0, _translate("Import", "UID"))
        self.cb2.setItemText(1, _translate("Import", "Password"))
        self.cb2.setItemText(2, _translate("Import", "Cookie"))
        self.cb2.setItemText(3, _translate("Import", "Mail"))
        self.cb2.setItemText(4, _translate("Import", "PassMail"))
        self.cb2.setItemText(5, _translate("Import", "UserCoin"))
        self.cb2.setItemText(6, _translate("Import", "PassCoin"))
        self.cb2.setItemText(7, _translate("Import", "Proxy"))
        self.cb2.setItemText(8, _translate("Import", "Useragent"))
        self.cb3.setItemText(0, _translate("Import", "UID"))
        self.cb3.setItemText(1, _translate("Import", "Password"))
        self.cb3.setItemText(2, _translate("Import", "Cookie"))
        self.cb3.setItemText(3, _translate("Import", "Mail"))
        self.cb3.setItemText(4, _translate("Import", "PassMail"))
        self.cb3.setItemText(5, _translate("Import", "UserCoin"))
        self.cb3.setItemText(6, _translate("Import", "PassCoin"))
        self.cb3.setItemText(7, _translate("Import", "Proxy"))
        self.cb3.setItemText(8, _translate("Import", "Useragent"))
        self.cb4.setItemText(0, _translate("Import", "UID"))
        self.cb4.setItemText(1, _translate("Import", "Password"))
        self.cb4.setItemText(2, _translate("Import", "Cookie"))
        self.cb4.setItemText(3, _translate("Import", "Mail"))
        self.cb4.setItemText(4, _translate("Import", "PassMail"))
        self.cb4.setItemText(5, _translate("Import", "UserCoin"))
        self.cb4.setItemText(6, _translate("Import", "PassCoin"))
        self.cb4.setItemText(7, _translate("Import", "Proxy"))
        self.cb4.setItemText(8, _translate("Import", "Useragent"))
        self.cb5.setItemText(0, _translate("Import", "UID"))
        self.cb5.setItemText(1, _translate("Import", "Password"))
        self.cb5.setItemText(2, _translate("Import", "Cookie"))
        self.cb5.setItemText(3, _translate("Import", "Mail"))
        self.cb5.setItemText(4, _translate("Import", "PassMail"))
        self.cb5.setItemText(5, _translate("Import", "UserCoin"))
        self.cb5.setItemText(6, _translate("Import", "PassCoin"))
        self.cb5.setItemText(7, _translate("Import", "Proxy"))
        self.cb5.setItemText(8, _translate("Import", "Useragent"))
        self.cb6.setItemText(0, _translate("Import", "UID"))
        self.cb6.setItemText(1, _translate("Import", "Password"))
        self.cb6.setItemText(2, _translate("Import", "Cookie"))
        self.cb6.setItemText(3, _translate("Import", "Mail"))
        self.cb6.setItemText(4, _translate("Import", "PassMail"))
        self.cb6.setItemText(5, _translate("Import", "UserCoin"))
        self.cb6.setItemText(6, _translate("Import", "PassCoin"))
        self.cb6.setItemText(7, _translate("Import", "Proxy"))
        self.cb6.setItemText(8, _translate("Import", "Useragent"))
        self.cb7.setItemText(0, _translate("Import", "UID"))
        self.cb7.setItemText(1, _translate("Import", "Password"))
        self.cb7.setItemText(2, _translate("Import", "Cookie"))
        self.cb7.setItemText(3, _translate("Import", "Mail"))
        self.cb7.setItemText(4, _translate("Import", "PassMail"))
        self.cb7.setItemText(5, _translate("Import", "UserCoin"))
        self.cb7.setItemText(6, _translate("Import", "PassCoin"))
        self.cb7.setItemText(7, _translate("Import", "Proxy"))
        self.cb7.setItemText(8, _translate("Import", "Useragent"))
        self.cb8.setItemText(0, _translate("Import", "UID"))
        self.cb8.setItemText(1, _translate("Import", "Password"))
        self.cb8.setItemText(2, _translate("Import", "Cookie"))
        self.cb8.setItemText(3, _translate("Import", "Mail"))
        self.cb8.setItemText(4, _translate("Import", "PassMail"))
        self.cb8.setItemText(5, _translate("Import", "UserCoin"))
        self.cb8.setItemText(6, _translate("Import", "PassCoin"))
        self.cb8.setItemText(7, _translate("Import", "Proxy"))
        self.cb8.setItemText(8, _translate("Import", "Useragent"))
        self.cb9.setItemText(0, _translate("Import", "UID"))
        self.cb9.setItemText(1, _translate("Import", "Password"))
        self.cb9.setItemText(2, _translate("Import", "Cookie"))
        self.cb9.setItemText(3, _translate("Import", "Mail"))
        self.cb9.setItemText(4, _translate("Import", "PassMail"))
        self.cb9.setItemText(5, _translate("Import", "UserCoin"))
        self.cb9.setItemText(6, _translate("Import", "PassCoin"))
        self.cb9.setItemText(7, _translate("Import", "Proxy"))
        self.cb9.setItemText(8, _translate("Import", "Useragent"))
        self.btnSave.setText(_translate("Import", "Lưu dữ liệu"))
        self.lblCoppyright.setText(_translate("Import", "Powered by "))
        self.lblPyTournes1.setText(F"{SHELLVERSION}")
        self.label_3.setText(_translate("Import", "- Developed by "))
        self.lblPyTournes2.setText(_translate("Import", "PyTournes"))
        self.label_5.setText(_translate("Import", ". Coppyright © 2023 "))
        self.lblPyTournes3.setText(_translate("Import", "PyTournes"))
        self.label_9.setText(_translate("Import", ". All rights reserved"))

        self.minimizeAppBtn.setEnabled(False)
        self.maximizeRestoreAppBtn.setEnabled(False)
        self.closeAppBtn.clicked.connect(self.closeAPP)
        self.APP = Import
        
        # Tạo ra 4 grip để resize
        for i in range(4):grip = QSizeGrip(self.stylesheet);grip.resize(self.gripSize, self.gripSize);self.grips.append(grip)
        self.APP.moveEvent = self.APP
        self.APP.mousePressEvent = self.mousePressEvent
        self.APP.mouseMoveEvent = self.mouseMoveEvent

        self.uiFuncions = UiFuncions(self)

        # Chức năng của titleBottom
        labels = [self.lblPyTournes1, self.lblPyTournes2, self.lblPyTournes3]
        for label in labels:label.mousePressEvent = self.openURL
        # - - - - - - - - - - - - - - - - - - - - - - - - - - -

        self.comboboxes = [self.cb1, self.cb2, self.cb3, self.cb4, self.cb5, self.cb6, self.cb7,self.cb8,self.cb9]
        self.item_texts = ["UID", "Password", "Cookie", "Email", "PassMail", "UserCoin", "PassCoin" ,"Proxy", "Useragent",""]

        self.btnSave.clicked.connect(self.importData)

        self.load_format();self.textImport.textChanged.connect(self.onTextChanged)

    def openURL(self, event):
        QDesktopServices.openUrl(QUrl("https://zalo.me/0865894536"))

    """Chức năng di chuyển zoom in zoom out APP"""
    def updateGripPositions(self):
        self.grips[0].move(0, 0) 
        self.grips[1].move(self.APP.width() - self.gripSize, 0) 
        self.grips[2].move(0, self.APP.height() - self.gripSize)  
        self.grips[3].move(self.APP.width() - self.gripSize, self.APP.height() - self.gripSize)

    def resizeEvent(self, event):
        self.APP.resizeEvent(event)
        self.updateGripPositions()

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self._old_pos = event.pos()
            
    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            self._old_pos = None
            
    def mouseMoveEvent(self, event):
        if not self._old_pos:
            return
        delta = event.pos() - self._old_pos
        self.APP.move(self.APP.pos() + delta)

    def closeAPP(self):  
        self.APP.close()

    """Chức năng Import Data"""
    def importData(self):
        try:
            with open(f"{pathConfig}/config.json", "r") as file:
                dataJson = json.load(file)
        except:
            dataJson = {}
        data = {}
        for combobox, text in zip(self.comboboxes, self.item_texts):
            selected_index = combobox.currentIndex()  # Lấy chỉ mục của mục được chọn
            selected_text = combobox.currentText()    # Lấy văn bản của mục được chọn
            if selected_text != '':data[selected_text] = (selected_index, selected_text)
        line_count = 0
        count_empty = sum(1 for value in data.values() if value[1] == '')
        for text in self.textImport.toPlainText().strip().split('\n'):
            line_count += 1
            if len(text.split('|')) >= len(data):pass
            else:self.uiFuncions.MessageBoxShow(text=f'Check the line format again {line_count}');return


        dataJson['inputFormat'] = data
        writeJson(f"{pathConfig}\\config.json", dataJson)
        with open(f'{pathData}/{self.folderName}.txt', 'a+') as f:
            f.write(self.textImport.toPlainText().strip()+'\n')
        if self.uiFuncions.MessageBoxShow(text='Save data successfully! \nDo you want to exit the program?'):self.APP.close()

    def onTextChanged(self):
        lines = self.textImport.toPlainText().split('\n')
        line_count_with_text = sum(1 for line in lines if line.strip() != "")
        self.label.setText("Danh sách tài khoản ( {} ):".format(line_count_with_text))

    def load_format(self):
        try:
            with open(f"{pathConfig}/config.json", "r") as file:
                dataJson = json.load(file)
        except:
            dataJson = {}
        for i in range(9):
            try:
                stt = int(list(dataJson['inputFormat'].values())[i][0]);self.comboboxes[i].setCurrentIndex(stt)
            except:
                self.comboboxes[i].setCurrentIndex(-1)
