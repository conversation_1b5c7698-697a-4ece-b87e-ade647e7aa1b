import requests, re
from io import BytesIO
import base64
import time
class Guru:
    def __init__(self, apikey):
        self.apikey = apikey
    
    def checkBalance(self):
        rsp  = requests.get(f'http://api.cap.guru/res.php?action=getbalance&key={self.apikey}').text 
        print(rsp)
        if int(rsp) > 0:return True
        return False
        
    
    def sliderCaptcha(self, url):
        response = requests.get(url)
        ee = base64.b64encode((response.content))
        payload = {'textinstructions': 'abc', 'click': 'geetest', 'key': self.apikey, 'method': 'base64', 'body': ee}
        r = requests.post("http://api.cap.guru/in.php", data=payload)
        rt = r.text.split('|')
        while True:
            url = 'http://api.cap.guru/res.php?key='+self.apikey+'&id='+rt[1]
            response = requests.get(url)
            if 'y=' in response.text:
                # OK|coordinates:x=410,y=294;x=364,y=296
                pairs = response.text.split(':')[1].split(';')
                result_list = []

                for pair in pairs:
                    x_val = int(pair.split('x=')[1].split(',')[0])
                    y_val = int(pair.split('y=')[1])
                    result_list.extend([x_val, y_val])
                return result_list
            if 'ERROR_CAPTCHA_UNSOLVABLE' in response.text:
                return 0
            time.sleep(3)
        
    def kolesoCaptcha(self, url1, url2):
        # print(url1,url2)
        ee1 = base64.b64encode((url1))
        ee2 = base64.b64encode((url2))
        payload = {'textinstructions': 'koleso', 'click': 'geetest', 'key': self.apikey, 'method': 'base64', 'body0': ee1, 'body1': ee2}
        r = requests.post("http://api.cap.guru/in.php", data=payload)
        rt = r.text.split('|')
        for t in range(10):
            url = 'http://api.cap.guru/res.php?key='+self.apikey+'&id='+rt[1]
            response = requests.get(url)
            # print(response.text)
            if 'CAPCHA_NOT_READY' not in response.text:
                return int(response.text.split('OK|coordinates:x=0,y=')[1])
            if 'ERROR_CAPTCHA_UNSOLVABLE' in response.text:
                return 0
            time.sleep(3)
        # OK|coordinates:x=0,y=189
 
        
# guru = Guru('90b618a51e23cc809ef7e0fde401d2ce')
# print(guru.sliderCaptcha('http://learn.captcha.guru/img/_images/geetest/ffd9736b8d7e9d82558271d777848b4f.png'))
# print(guru.checkBalance())
#  
# print(guru.kolesoCaptcha(b'https://sf16-website-login.neutral.ttwstatic.com/obj/tiktok_web_login_static/tiktok/webapp/main/webapp-desktop/045b2fc7c278b9a30dd0.png',b'https://p16-sign-useast2a.tiktokcdn.com/tos-useast2a-avt-0068-euttp/8feb87da7a55e1db39763163f55af684~c5_100x100.jpeg?x-expires=1702911600&x-signature=dkTsPi1bUPZLNSrg3gH6j0%2BiQOM%3D'))