from library import *
# from src.funcions import *
# from src.miningCoin import *
from src.api import *

for n in [pathConfig, pathOutput, pathData, pathHistory, pathLog, pathChrome, pathCache, pathDataTikTok]:
    if os.path.exists(n) == False:
        os.makedirs(n)
    
def writeFile(nameFile: str, text: str):
    o = open(nameFile, "a+",encoding='utf-8')
    o.write(text)
    o.close() 

def writeJson(nameFile: str, dataJson):
    with open(nameFile, "w", encoding='utf-8') as outfile:
        json.dump(dataJson, outfile, indent=4) 

def emptyFile(nameFile: str):
    with open(nameFile, 'w', encoding='utf-8') as file:
        file.write("")

def getWindow_params(totalIndex, row, columns, width=1200, height=1800  ):
    if columns == 0:
        return 0, 0, width, height  # Tr<PERSON> về giá trị mặc định nếu columns bằng 0
    else:
        width_per_column = width / columns *6
        x = (totalIndex % columns) * width_per_column  # Vị trí x
        height_per_row = height / row 
        y = (totalIndex // columns) * height_per_row  # Vị trí y
        return x, y, width, height

def delayQThread(time: int):
    delay = QEventLoop()
    QTimer.singleShot(int(time * 1000), delay.quit)
    delay.exec_()

def get_ip_address():
    try:
        return requests.get('https://ipinfo.io/json').json()['ip']
    except: return 0
    
def get_id_win():
    listText = list(string.ascii_letters + string.digits)
    pathData = os.path.abspath(os.environ['PROGRAMDATA']+"\\PyTournes\\AutoTikTok\\Device.ini")
    if os.path.exists(pathData.split("\\"+pathData.split("\\")[-1])[0]) == False:
        os.makedirs(pathData.split("\\"+pathData.split("\\")[-1])[0])
    if os.path.exists(pathData) == False:
        Device = ""
        for x in range(1000):
            Device += random.choice(listText)
        open(pathData, "w+").write(Device)
    else:
        Device = open(pathData, "r").read()
    key1 =  ''.join(str(subprocess.check_output('wmic csproduct get uuid').decode('utf-8').split("\n")[1].split("  ")[0]).split("-"))
    key2 = ''.join(subprocess.check_output("wmic computersystem get model,name").decode('utf-8').split("\r\r\n")[1].split('  '))
    return hashlib.md5(bytes(str(key1+key2+str(os.environ['USERNAME']) + str(platform.platform()) + str(platform.machine()) + str(Device)), 'utf-8-sig')).hexdigest()

def convertdate(date_string):
    date_object = datetime.strptime(date_string, '%Y-%m-%d')
    new_date_string = date_object.strftime('%d/%m/%Y')
    return new_date_string


def getWindowParams(width, height , thread = 60):
    global window_params
    # width = 1200, height = 1450
    for i in range(thread):
        x = (i % 11) * width 
        y = (i // 11) * height
        window_params.append((i, x, y, width, height))


def get_unused_window_params():
    global used_indexes
    for i in window_params:
        if i not in used_indexes:
            used_indexes.append(i)  # Đánh dấu chỉ mục đã sử dụng
            return i
        
    return None

userWin = os.environ['USERNAME']
idWin = get_id_win()
locale.setlocale(locale.LC_ALL, 'C')

window_params = []; used_indexes = []
SHELLVERSION = "PyTournes Shell v1.8.4 "
