from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QWindow
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QApplication, QScrollArea, QGridLayout
import win32con
import win32gui

class Ui_Viewer(QWidget):
    def __init__(self, *args, **kwargs):
        super(Ui_Viewer, self).__init__(*args, **kwargs)
        self.setWindowTitle('PyTournes - Viewer')  # Set the window title

        self.showMaximized()  # Show the window maximized

        # Create a scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        
        # Create a widget to hold the layout
        widget = QWidget()
        self.main_layout = QGridLayout(widget)
        scroll_area.setWidget(widget)
        
        # Set the layout for the main window
        self.setLayout(QVBoxLayout(self))
        self.layout().addWidget(scroll_area)

        self.myhwnd = int(self.winId())
        print(self.myhwnd)

        self.timer = QTimer(self)
        self.timer.timeout.connect(self._getWindowList)
        self.timer.start(1000)  # Update every second

        self.row = 0
        self.col = 0

    def _getWindowList(self):
        win32gui.EnumWindows(self._enumWindows, None)
        print('ok')

    def onItemDoubleClicked(self, hwnd, phwnd, style, exstyle):
        widget = QWidget.createWindowContainer(QWindow.fromWinId(hwnd))
        widget.setFixedHeight(400)
        widget.hwnd = hwnd
        widget.phwnd = phwnd
        widget.style = style
        widget.exstyle = exstyle
        widget.setParent(self)
        self.main_layout.addWidget(widget, self.row, self.col)
        self.col += 1
        if self.col > 1:
            self.row += 1
            self.col = 0

    def _enumWindows(self, hwnd, _):
        if hwnd == self.myhwnd:
            return
        if win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd) and win32gui.IsWindowEnabled(hwnd):
            title = win32gui.GetWindowText(hwnd)
            if "ChromeON:" in title:  # Filter windows with title "Tournes"
                phwnd = win32gui.GetParent(hwnd)
                name = win32gui.GetClassName(hwnd)
                self.onItemDoubleClicked(hwnd, phwnd, win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE), win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE))
                win32gui.SetWindowText(hwnd, 'Embed')
                print(win32gui.GetWindowText(hwnd))
                print('Embed', hwnd, name)


