from library import *
class MailTm:
    def __init__(self, mail: str , passmail: str, domain = ''):
        self.mail , self.passmail, self.domain = mail, passmail ,domain
        
    def addProxy(self, proxy: str):
        try:
            if len(proxy.split(':')) >= 4:
                parts = proxy.split(':')
                iport = ":".join(parts[:2])
                userpass = ":".join(parts[2:])
                self.proxyDict = {
                    'https': 'http://{}@{}'.format(userpass,iport),
                    'http': 'http://{}@{}'.format(userpass,userpass)
                    }
            elif len(proxy.split(':')) >= 2:
                proxy_url = 'http://{}'.format(proxy)
                self.proxyDict   = { 
                "http"  : proxy_url, 
                "https" : proxy_url
            }
            else:
                self.proxyDict   = ''

            ip = requests.get('https://www.myip.com/', proxies=self.proxyDict).text
            # print('My ip address:',ip.split('<span id="ip">')[1].split('</span>')[0],end='\r')
            return True
        except: return False
        
    def registerEmail(self):
        user = ["a","b","c","d","e","f","g","h","u","i","o","y","m","n","l","h","q","x","s","k","p","t","w","v","j","z"]
        self.mail = ""
        for i in range(4):
            num = str(random.randint(1,100))
            self.mail += random.choice(user)
            self.mail += num
        self.mail += "@"+self.domain
        data = '{"address":"'+self.mail+'","password":"'+self.passmail+'"}'
        rsp = requests.post("https://api.mail.tm/accounts", data, headers={"content-type":"application/json"},proxies=self.proxyDict).text
        if '@id' in rsp and self.getToken():
            return self.mail
        else:return False

        
    def getToken(self):
        # try:
            response = requests.post('https://api.mail.tm/token', json={
                'address': self.mail,
                'password': self.passmail,
            },proxies=self.proxyDict).json()
            self.token = response['token']
            return True
        # except:pass
        # return False
        
    def getCodeTikTok(self):
        try:
            messages = requests.get("https://api.mail.tm/messages", headers={"authorization": "Bearer " + self.token}, proxies=self.proxyDict).text
            match = re.search(r'"subject":"(\d{6})', messages)
            if match:code = match.group(1);return code
        except: pass
        return 0

# domain = requests.get("https://api.mail.tm/domains?page=1", headers={"content-type":"application/json"}).json()["hydra:member"][0]["domain"]

# # # name = ''.join(random.choice(string.ascii_letters + string.digits + string.punctuation) for _ in range(8))
# # # print(name)
# __mail = MailTm('<EMAIL>','PyT0urnes.i0.vn',domain)
# __mail.addProxy('')
# # print(__mail.registerEmail())
# print(__mail.getToken())
# print(__mail.getCodeTikTok())