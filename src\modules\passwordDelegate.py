from library import *

class PasswordDelegate(QtWidgets.QStyledItemDelegate):
    def setColumn(self, iColumn: int):
        self.iColumn = int(iColumn)
    def initStyleOption(self, option, index):
        super().initStyleOption(option, index)
        if index.column() == self.iColumn or index.column() == 2 or index.column() == 6:
            style = option.widget.style() or QtWidgets.QApplication.style()
            hint = style.styleHint(QtWidgets.QStyle.SH_LineEdit_PasswordCharacter)
            option.text = chr(hint) * 8

