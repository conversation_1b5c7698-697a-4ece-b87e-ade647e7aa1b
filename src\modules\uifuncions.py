from library import *

class UiFuncions:
    def __init__(self, parent_widget):
        super().__init__()
        self.parent = parent_widget
        self.styleDiaLog = QInputDialog()
        with open('src/themes/qinputdialog.css','r') as file:
            self.styleDiaLog.setStyleSheet(file.read())
    def MessageBoxShow(self, title= 'Notification', text= ''):
        msg = QMessageBox(QMessageBox.Information, title, text, QMessageBox.Ok | QMessageBox.Cancel)
        with open('src/themes/qmessagebox.css','r') as file:
            msg.setStyleSheet(file.read())
        icon = QIcon("./images/images/information.png")
        msg.setWindowIcon(icon)
        retval = msg.exec_()
        if retval == QMessageBox.Ok:return True
        return False

    def InputDiaLog(self, title="Notification", text1="", text2=""):
        nameFolder, ok = QtWidgets.QInputDialog().getItem(self.styleDiaLog, title, text1, 
                                                  text2, 
                                                  editable=True, current=0)
        return nameFolder,ok

    def InputDiaLogGetText(self, title= 'Notification', text1= '', text2= ''):
       
        nameFolder, ok = QInputDialog.getText(self.styleDiaLog, title, text1, QLineEdit.Normal, text2)
        return nameFolder,ok
    
    def DialogFolder(self):
        self.filepath = QtWidgets.QFileDialog()
        self.filepath.setFileMode(QtWidgets.QFileDialog.Directory) # chọn thư mục
        self.filepath.show()
        if self.filepath.exec_() == QtWidgets.QDialog.Accepted: 
            return self.filepath.selectedFiles()[0]