from library import *


class ActionTable:
    def __init__(self, parent_widget):
        super().__init__()
        self.parent = parent_widget
        
    def CellClicked(self,gridView, posButton: int):
        row = gridView.currentRow()
        # print(self.parent.tableWidget.item(row, posButton).text())
        if gridView.item(row, posButton).text() == "Start": 
            self.parent.startMining(row)
            self.EnableStop(row,gridView,posButton)
        elif gridView.item(row, posButton).text() == "Stop":  
            self.EnableStart(row,gridView,posButton)
            self.parent.stopMining(row)
        else: self.EnableStart(row,gridView,posButton)

    def EnableStart(self, row, gridView, posButton):
        gridView.setItem(row, int(posButton), QtWidgets.QTableWidgetItem("Start"))
        btn = QtWidgets.QPushButton("Start")
        btn.setStyleSheet("QPushButton{background-color: #28a745; color: white; border: 2px solid #28a745; border-radius: 5px;}")
        btn.clicked.connect(lambda:self.CellClicked(gridView, posButton))
        gridView.setCellWidget(row, int(posButton), btn)
    
    def EnableStop(self, row, gridView, posButton):
        gridView.setItem(row, int(posButton), QtWidgets.QTableWidgetItem("Stop"))
        btn = QtWidgets.QPushButton("Stop")
        btn.setStyleSheet("QPushButton{background-color: #dc3545; color: white; border: 2px solid #dc3545; border-radius: 5px;}")
        btn.clicked.connect(lambda:self.CellClicked(gridView, posButton))
        gridView.setCellWidget(row, int(posButton), btn)